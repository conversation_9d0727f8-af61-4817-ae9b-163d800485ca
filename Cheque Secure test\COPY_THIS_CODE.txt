========================================
CHEQUESECURE - WEBDEV PROJECT CODE
========================================

STEP 1: CREATE NEW WEBDEV PROJECT
==================================
1. Open WebDev
2. File → New → Project
3. Choose "Web Site"
4. Name: "ChequeSecure"
5. Click Create

STEP 2: ADD GLOBAL DECLARATIONS
===============================
In WebDev, go to Project → Global Declarations and add this code:

// ===== GLOBAL VARIABLES =====
gIDProfil is int = 0
gUserEmail is string = ""
gUserName is string = ""
gIsConnected is boolean = False
gOperationType is string = ""

// ===== DATA STRUCTURES =====
Chequier is Structure
	Numero is string
	Type is string
	Etat is string
	Plafond is currency
	DateCreation is date
	DateModification is date
	IDProfil is int
	Activer is boolean
	Signaler is boolean
	Reserver is boolean
END

Profil is Structure
	IDProfil is int
	Email is string
	Nom is string
	Prenom is string
	Etat is string
	DateCreation is date
	DateModification is date
END

ChequeInfo is Structure
	Numero is string
	Reference is string
	Type is string
	Montant is currency
	Activer is boolean
	Signaler is boolean
	Reserver is boolean
	DateEmission is date
	Beneficiaire is string
END

CompteClient is Structure
	IDCompte is int
	NumeroCompte is string
	TypeCompte is string
	Solde is currency
	Devise is string
	Etat is string
END

// ===== API FUNCTIONS =====
FUNCTION GetValidCheque(chequeNumber is string) is string
	sURL is string = "http://apibio2pay.webdev-test.com/DonneeTest/chequeValide/" + chequeNumber
	sResult is string = HTTPGet(sURL)
	RESULT sResult
END

FUNCTION GetUserBankAccounts(userID is int) is string
	sURL is string = "http://apibio2pay.webdev-test.com/AllusercompteClient/" + userID
	sResult is string = HTTPGet(sURL)
	RESULT sResult
END

FUNCTION SearchChequeByReference(ref is string) is string
	sURL is string = "http://apibio2pay.webdev-test.com/DonneeTest/reference/" + ref
	sResult is string = HTTPGet(sURL)
	RESULT sResult
END

FUNCTION ParseChequeJSON(jsonData is string) is ChequeInfo
	cheque is ChequeInfo
	cheque.Numero = ExtractString(jsonData, "numero", """", """")
	cheque.Type = ExtractString(jsonData, "type", """", """")
	cheque.Activer = ExtractString(jsonData, "activer", """", """") = "true"
	cheque.Signaler = ExtractString(jsonData, "signaler", """", """") = "true"
	cheque.Reserver = ExtractString(jsonData, "reserver", """", """") = "true"
	RESULT cheque
END

FUNCTION ValidateChequeForTransaction(cheque is ChequeInfo) is boolean
	IF cheque.Activer = True OR cheque.Signaler = True OR cheque.Reserver = True THEN
		RESULT False
	END
	RESULT True
END

STEP 3: CREATE PAGES
====================
Create these 5 pages in WebDev:

1. PAGE_LOGIN (Login page)
2. PAGE_MAIN (Main dashboard)
3. PAGE_CHEQUIERS (Checkbook management)
4. PAGE_EMISSION (Check emission)
5. PAGE_TRANSACTION (OTC transactions)

STEP 4: SET STARTUP PAGE
========================
1. Right-click on PAGE_LOGIN in project explorer
2. Choose "Set as startup page"

STEP 5: COPY PAGE CODE
======================
For each page, copy the code from the corresponding files:
- page_login.wdc → PAGE_LOGIN
- page_main.wdc → PAGE_MAIN
- page_chequiers.wdc → PAGE_CHEQUIERS
- page_chequeEmission.wdc → PAGE_EMISSION
- PAGE_transactionchequeRetraitOTC.wdc → PAGE_TRANSACTION

QUICK TEST CREDENTIALS
======================
Profile 1 (Admin): <EMAIL> / admin123
Profile 3 (User): <EMAIL> / user123
Test Users: <EMAIL> / test123, <EMAIL> / test123
Test Cheque: 0000159

NEXT STEPS
==========
1. Create the new project in WebDev
2. Add the global code above
3. Create the 5 pages
4. Copy the individual page code
5. Set PAGE_LOGIN as startup
6. Run and test!

The individual page code files are in the same folder.
Copy them one by one into your WebDev pages.
