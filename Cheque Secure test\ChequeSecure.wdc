[PROJECT]
Name=ChequeSecure
Version=1.0
Description=ChèqueSecure - Secure Check Management System
Author=Developer
CreationDate=2024-01-01
LastModificationDate=2024-01-01
Language=English
Framework=WebDev
Type=WebSite

[GLOBAL_VARIABLES]
// Global variables for profile management
gIDProfil is int = 1
gUserEmail is string
gUserName is string
gIsConnected is boolean = False

[PAGES]
page_chequiers=profilsOTC/page_chequiers.wdc
page_chequeEmission=page_emission/page_chequeEmission.wdc
PAGE_transactionchequeRetraitOTC=emission/PAGE_transactionchequeRetraitOTC.wdc

[NAVIGATION]
MainMenu=Gestion,Émission
Gestion=page_chequiers
Émission=page_chequeEmission

[SETTINGS]
DefaultPage=page_chequiers
Theme=Default
ResponsiveDesign=True

[EXTERNAL_FUNCTIONS]
// API Functions for external services
FUNCTION GetValidCheque(chequeNumber is string) is string
// API: http://apibio2pay.webdev-test.com/DonneeTest/chequeValide/0000159
// Returns JSON with cheque details

FUNCTION GetUserBankAccounts(userID is int) is string
// API: http://apibio2pay.webdev-test.com/AllusercompteClient/3
// Returns JSON with user bank accounts

FUNCTION SearchChequeByReference(ref is string) is string
// API: http://apibio2pay.webdev-test.com/DonneeTest/reference/"+ref
// Returns JSON with cheque details 