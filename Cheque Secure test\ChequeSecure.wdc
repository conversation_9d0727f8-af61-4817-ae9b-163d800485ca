[PROJECT]
Name=ChequeSecure
Version=1.0
Description=ChèqueSecure - Secure Check Management System
Author=Developer
CreationDate=2024-01-01
LastModificationDate=2024-01-01
Language=English
Framework=WebDev
Type=WebSite

[GLOBAL_VARIABLES]
// Global variables for profile management
gIDProfil is int = 0
gUserEmail is string = ""
gUserName is string = ""
gIsConnected is boolean = False
gOperationType is string = ""  // Used to pass operation type between pages

// Data structures
Chequier is Structure
	Numero is string
	Type is string
	Etat is string
	Plafond is currency
	DateCreation is date
	DateModification is date
	IDProfil is int
	Activer is boolean
	Signaler is boolean
	Reserver is boolean
END

Profil is Structure
	IDProfil is int
	Email is string
	Nom is string
	Prenom is string
	Etat is string
	DateCreation is date
	DateModification is date
END

ChequeInfo is Structure
	Numero is string
	Reference is string
	Type is string
	Montant is currency
	Activer is boolean
	Signaler is boolean
	Reserver is boolean
	DateEmission is date
	Beneficiaire is string
END

CompteClient is Structure
	IDCompte is int
	NumeroCompte is string
	TypeCompte is string
	Solde is currency
	Devise is string
	Etat is string
END

[API_FUNCTIONS]
// API Integration Functions
FUNCTION GetValidCheque(chequeNumber is string) is string
	sURL is string = "http://apibio2pay.webdev-test.com/DonneeTest/chequeValide/" + chequeNumber
	sResult is string = HTTPGet(sURL)
	RESULT sResult
END

FUNCTION GetUserBankAccounts(userID is int) is string
	sURL is string = "http://apibio2pay.webdev-test.com/AllusercompteClient/" + userID
	sResult is string = HTTPGet(sURL)
	RESULT sResult
END

FUNCTION SearchChequeByReference(ref is string) is string
	sURL is string = "http://apibio2pay.webdev-test.com/DonneeTest/reference/" + ref
	sResult is string = HTTPGet(sURL)
	RESULT sResult
END

FUNCTION ParseChequeJSON(jsonData is string) is ChequeInfo
	cheque is ChequeInfo
	// Parse JSON response and populate structure
	// This is a simplified version - in real implementation use JSON parsing functions
	cheque.Numero = ExtractString(jsonData, "numero", """", """")
	cheque.Type = ExtractString(jsonData, "type", """", """")
	cheque.Activer = ExtractString(jsonData, "activer", """", """") = "true"
	cheque.Signaler = ExtractString(jsonData, "signaler", """", """") = "true"
	cheque.Reserver = ExtractString(jsonData, "reserver", """", """") = "true"
	RESULT cheque
END

FUNCTION ValidateChequeForTransaction(cheque is ChequeInfo) is boolean
	// Check if cheque is valid for transaction
	IF cheque.Activer = True OR cheque.Signaler = True OR cheque.Reserver = True THEN
		RESULT False
	END
	RESULT True
END

[PAGES]
page_login=page_login.wdc
page_main=page_main.wdc
page_chequiers=profilsOTC/page_chequiers.wdc
page_chequeEmission=page_emission/page_chequeEmission.wdc
PAGE_transactionchequeRetraitOTC=emission/PAGE_transactionchequeRetraitOTC.wdc

[NAVIGATION]
MainMenu=Connexion,Accueil,Gestion,Émission
Connexion=page_login
Accueil=page_main
Gestion=page_chequiers
Émission=page_chequeEmission

[SETTINGS]
DefaultPage=page_login
Theme=Default
ResponsiveDesign=True
Language=French
Charset=UTF-8
SessionTimeout=30
SecurityLevel=High

[EXTERNAL_FUNCTIONS]
// API Functions for external services
FUNCTION GetValidCheque(chequeNumber is string) is string
// API: http://apibio2pay.webdev-test.com/DonneeTest/chequeValide/0000159
// Returns JSON with cheque details

FUNCTION GetUserBankAccounts(userID is int) is string
// API: http://apibio2pay.webdev-test.com/AllusercompteClient/3
// Returns JSON with user bank accounts

FUNCTION SearchChequeByReference(ref is string) is string
// API: http://apibio2pay.webdev-test.com/DonneeTest/reference/"+ref
// Returns JSON with cheque details 