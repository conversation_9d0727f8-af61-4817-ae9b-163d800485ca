[PROJECT]
Name=ChequeSecure
Version=28
Type=Site
Description=ChèqueSecure - Secure Check Management System
Author=Developer
Company=ChèqueSecure
Copyright=2024
Language=French
Framework=WebDev

[CONFIGURATION]
Configuration=Development
Platform=Web
Target=HTML5
Compilation=Dynamic
Debug=Yes
Optimization=No

[PAGES]
page_login
page_main
page_chequiers
page_chequeEmission
PAGE_transactionchequeRetraitOTC

[GLOBAL_PROCEDURES]
// Global variables for profile management
gIDProfil is int = 0
gUserEmail is string = ""
gUserName is string = ""
gIsConnected is boolean = False
gOperationType is string = ""

// Data structures
Chequier is Structure
	Numero is string
	Type is string
	Etat is string
	Plafond is currency
	DateCreation is date
	DateModification is date
	IDProfil is int
	Activer is boolean
	Signaler is boolean
	Reserver is boolean
END

Profil is Structure
	IDProfil is int
	Email is string
	Nom is string
	Prenom is string
	Etat is string
	DateCreation is date
	DateModification is date
END

ChequeInfo is Structure
	Numero is string
	Reference is string
	Type is string
	Montant is currency
	Activer is boolean
	Signaler is boolean
	Reserver is boolean
	DateEmission is date
	Beneficiaire is string
END

CompteClient is Structure
	IDCompte is int
	NumeroCompte is string
	TypeCompte is string
	Solde is currency
	Devise is string
	Etat is string
END

// API Integration Functions
FUNCTION GetValidCheque(chequeNumber is string) is string
	sURL is string = "http://apibio2pay.webdev-test.com/DonneeTest/chequeValide/" + chequeNumber
	sResult is string = HTTPGet(sURL)
	RESULT sResult
END

FUNCTION GetUserBankAccounts(userID is int) is string
	sURL is string = "http://apibio2pay.webdev-test.com/AllusercompteClient/" + userID
	sResult is string = HTTPGet(sURL)
	RESULT sResult
END

FUNCTION SearchChequeByReference(ref is string) is string
	sURL is string = "http://apibio2pay.webdev-test.com/DonneeTest/reference/" + ref
	sResult is string = HTTPGet(sURL)
	RESULT sResult
END

FUNCTION ParseChequeJSON(jsonData is string) is ChequeInfo
	cheque is ChequeInfo
	// Parse JSON response and populate structure
	cheque.Numero = ExtractString(jsonData, "numero", """", """")
	cheque.Type = ExtractString(jsonData, "type", """", """")
	cheque.Activer = ExtractString(jsonData, "activer", """", """") = "true"
	cheque.Signaler = ExtractString(jsonData, "signaler", """", """") = "true"
	cheque.Reserver = ExtractString(jsonData, "reserver", """", """") = "true"
	RESULT cheque
END

FUNCTION ValidateChequeForTransaction(cheque is ChequeInfo) is boolean
	// Check if cheque is valid for transaction
	IF cheque.Activer = True OR cheque.Signaler = True OR cheque.Reserver = True THEN
		RESULT False
	END
	RESULT True
END

[NAVIGATION]
StartPage=page_login
MainMenu=Connexion,Accueil,Gestion,Émission
Connexion=page_login
Accueil=page_main
Gestion=page_chequiers
Émission=page_chequeEmission

[SETTINGS]
Theme=Default
ResponsiveDesign=True
Language=French
Charset=UTF-8
SessionTimeout=30
SecurityLevel=High

[FILES]
page_login.wdw
page_main.wdw
profilsOTC\page_chequiers.wdw
page_emission\page_chequeEmission.wdw
emission\PAGE_transactionchequeRetraitOTC.wdw
