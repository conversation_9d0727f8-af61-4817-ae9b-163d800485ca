<?xml version="1.0" encoding="UTF-8"?>
<Project>
    <Information>
        <Name>ChequeSecure</Name>
        <Version>1.0</Version>
        <Type>WebSite</Type>
        <Description>ChèqueSecure - Secure Check Management System</Description>
        <Author>Developer</Author>
        <Company>ChèqueSecure</Company>
        <Copyright>2024</Copyright>
        <Language>French</Language>
        <Framework>WebDev</Framework>
        <Target>HTML5</Target>
        <Platform>Web</Platform>
    </Information>
    
    <Configuration>
        <Development>
            <Debug>true</Debug>
            <Optimization>false</Optimization>
            <Compilation>Dynamic</Compilation>
        </Development>
        <Production>
            <Debug>false</Debug>
            <Optimization>true</Optimization>
            <Compilation>Static</Compilation>
        </Production>
    </Configuration>
    
    <Pages>
        <Page name="page_login" file="page_login.wdw" startup="true" />
        <Page name="page_main" file="page_main.wdw" />
        <Page name="page_chequiers" file="profilsOTC/page_chequiers.wdw" />
        <Page name="page_chequeEmission" file="page_emission/page_chequeEmission.wdw" />
        <Page name="PAGE_transactionchequeRetraitOTC" file="emission/PAGE_transactionchequeRetraitOTC.wdw" />
    </Pages>
    
    <GlobalProcedures>
        <File>global_procedures.wdg</File>
    </GlobalProcedures>
    
    <Navigation>
        <StartPage>page_login</StartPage>
        <Menu>
            <Item name="Connexion" page="page_login" />
            <Item name="Accueil" page="page_main" />
            <Item name="Gestion" page="page_chequiers" />
            <Item name="Émission" page="page_chequeEmission" />
        </Menu>
    </Navigation>
    
    <Settings>
        <Theme>Default</Theme>
        <ResponsiveDesign>true</ResponsiveDesign>
        <Language>French</Language>
        <Charset>UTF-8</Charset>
        <SessionTimeout>30</SessionTimeout>
        <SecurityLevel>High</SecurityLevel>
    </Settings>
    
    <Resources>
        <Styles>
            <File>styles/main.css</File>
        </Styles>
        <Scripts>
            <File>scripts/main.js</File>
        </Scripts>
    </Resources>
    
    <APIs>
        <Endpoint name="GetValidCheque" url="http://apibio2pay.webdev-test.com/DonneeTest/chequeValide/" />
        <Endpoint name="GetUserBankAccounts" url="http://apibio2pay.webdev-test.com/AllusercompteClient/" />
        <Endpoint name="SearchChequeByReference" url="http://apibio2pay.webdev-test.com/DonneeTest/reference/" />
    </APIs>
</Project>
