PROJECT=ChequeSecure
VERSION=1.0
TYPE=SITE
DESCRIPTION=ChèqueSecure - Secure Check Management System
AUTHOR=Developer
LANGUAGE=FRENCH

PAGES=5
PAGE1=page_login
PAGE2=page_main  
PAGE3=page_chequiers
PAGE4=page_chequeEmission
PAGE5=PAGE_transactionchequeRetraitOTC

STARTPAGE=page_login

GLOBAL_VARIABLES
gIDProfil is int = 0
gUserEmail is string = ""
gUserName is string = ""
gIsConnected is boolean = False
gOperationType is string = ""

STRUCTURES
Chequier is Structure
	Numero is string
	Type is string
	Etat is string
	Plafond is currency
	DateCreation is date
	DateModification is date
	IDProfil is int
	Activer is boolean
	Signaler is boolean
	Reserver is boolean
END

Profil is Structure
	IDProfil is int
	Email is string
	Nom is string
	Prenom is string
	Etat is string
	DateCreation is date
	DateModification is date
END

ChequeInfo is Structure
	Numero is string
	Reference is string
	Type is string
	Montant is currency
	Activer is boolean
	Signaler is boolean
	Reserver is boolean
	DateEmission is date
	Beneficiaire is string
END

CompteClient is Structure
	IDCompte is int
	NumeroCompte is string
	TypeCompte is string
	Solde is currency
	Devise is string
	Etat is string
END

GLOBAL_FUNCTIONS
FUNCTION GetValidCheque(chequeNumber is string) is string
	sURL is string = "http://apibio2pay.webdev-test.com/DonneeTest/chequeValide/" + chequeNumber
	sResult is string = HTTPGet(sURL)
	RESULT sResult
END

FUNCTION GetUserBankAccounts(userID is int) is string
	sURL is string = "http://apibio2pay.webdev-test.com/AllusercompteClient/" + userID
	sResult is string = HTTPGet(sURL)
	RESULT sResult
END

FUNCTION SearchChequeByReference(ref is string) is string
	sURL is string = "http://apibio2pay.webdev-test.com/DonneeTest/reference/" + ref
	sResult is string = HTTPGet(sURL)
	RESULT sResult
END

FUNCTION ParseChequeJSON(jsonData is string) is ChequeInfo
	cheque is ChequeInfo
	cheque.Numero = ExtractString(jsonData, "numero", """", """")
	cheque.Type = ExtractString(jsonData, "type", """", """")
	cheque.Activer = ExtractString(jsonData, "activer", """", """") = "true"
	cheque.Signaler = ExtractString(jsonData, "signaler", """", """") = "true"
	cheque.Reserver = ExtractString(jsonData, "reserver", """", """") = "true"
	RESULT cheque
END

FUNCTION ValidateChequeForTransaction(cheque is ChequeInfo) is boolean
	IF cheque.Activer = True OR cheque.Signaler = True OR cheque.Reserver = True THEN
		RESULT False
	END
	RESULT True
END

NAVIGATION
MENU=Connexion,Accueil,Gestion,Émission
Connexion=page_login
Accueil=page_main
Gestion=page_chequiers
Émission=page_chequeEmission

SETTINGS
THEME=Default
RESPONSIVE=True
LANGUAGE=French
CHARSET=UTF-8
TIMEOUT=30
SECURITY=High

API_ENDPOINTS
GetValidCheque=http://apibio2pay.webdev-test.com/DonneeTest/chequeValide/
GetUserBankAccounts=http://apibio2pay.webdev-test.com/AllusercompteClient/
SearchChequeByReference=http://apibio2pay.webdev-test.com/DonneeTest/reference/
