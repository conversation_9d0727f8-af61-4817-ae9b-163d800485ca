[PAGE]
Name=PAGE_transactionchequeRetraitOTC
Title=Retrait/Paiement par Chèque OTC
Description=Page pour retrait ou paiement par chèque OTC

[CONTROLS]
EDT_CHEQUE_NUMBER is Edit
{
    Placeholder="Numéro de chèque"
}
BTN_GET_CHEQUE is Button
{
    Text="Obtenir les infos du chèque"
    OnClick=BTN_GET_CHEQUE_Click
}
EDT_REF is Edit
{
    Placeholder="Référence"
}
BTN_SEARCH_REF is Button
{
    Text="Rechercher par référence"
    OnClick=BTN_SEARCH_REF_Click
}
STC_RESULT is Static
{
    Text=""
}

[EVENTS]
PROCEDURE BTN_GET_CHEQUE_Click()
chequeNumber is string = EDT_CHEQUE_NUMBER
chequeData is string = HTTPGet("http://apibio2pay.webdev-test.com/DonneeTest/chequeValide/" + chequeNumber)
STC_RESULT = chequeData

PROCEDURE BTN_SEARCH_REF_Click()
ref is string = EDT_REF
chequeData is string = HTTPGet("http://apibio2pay.webdev-test.com/DonneeTest/reference/" + ref)
STC_RESULT = chequeData 