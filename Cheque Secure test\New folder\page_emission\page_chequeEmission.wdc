[PAGE]
Name=page_chequeEmission
Title=Émission de Chèques
Description=Page d'émission de chèques avec quatre types d'opérations

[CONTROLS]
BTN_RETRAIT is Button
{
    Text="Retrait par chèque OTC"
    OnClick=BTN_RETRAIT_Click
}
BTN_PAIEMENT is Button
{
    Text="Paiement par chèque OTC"
    OnClick=BTN_PAIEMENT_Click
}
BTN_CLASSIQUE is Button
{
    Text="Émission de chèque classique"
    OnClick=BTN_CLASSIQUE_Click
}
BTN_PREPAYE is Button
{
    Text="Émission de chèque prépayé"
    OnClick=BTN_PREPAYE_Click
}
STC_INFO is Static
{
    Text=""
}

[EVENTS]
PROCEDURE Page_Load()
IF gIDProfil <> 1 AND gIDProfil <> 3 THEN
    STC_INFO = "Access denied. Profile 1 or 3 required."
    RETURN
END

PROCEDURE BTN_RETRAIT_Click()
PageDisplay("emission/PAGE_transactionchequeRetraitOTC")

PROCEDURE BTN_PAIEMENT_Click()
PageDisplay("emission/PAGE_transactionchequeRetraitOTC")

PROCEDURE BTN_CLASSIQUE_Click()
STC_INFO = "Classic cheque emission selected."

PROCEDURE BTN_PREPAYE_Click()
STC_INFO = "Prepaid cheque emission selected." 