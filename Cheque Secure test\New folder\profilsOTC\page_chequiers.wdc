[PAGE]
Name=page_chequiers
Title=Vos Chéquiers
Description=Gestion des chéquiers de l'utilisateur connecté

[VARIABLES]
// Local variables for this page
lstChequiers is array of Chequier
lstProfils is array of Profil
selectedChequier is int = -1
searchEmail is string
newProfil is Profil

[CONTROLS]
// Main container
MainContainer is Container {
    Width: 100%
    Height: 100%
    BackgroundColor: #f8f9fa
}

// Header section
HeaderSection is Container {
    Width: 100%
    Height: 80px
    BackgroundColor: #007bff
    Color: white
    TextAlign: center
    Padding: 20px
}

HeaderTitle is Static {
    Text: "Vos Chéquiers"
    FontSize: 24px
    FontWeight: bold
}

// Content section
ContentSection is Container {
    Width: 100%
    Height: calc(100% - 80px)
    Padding: 20px
}

// Checkbooks list section
ChequiersSection is Container {
    Width: 100%
    Height: 60%
    BackgroundColor: white
    Border: 1px solid #dee2e6
    BorderRadius: 8px
    Padding: 20px
    MarginBottom: 20px
}

SectionTitle is Static {
    Text: "Liste de vos chéquiers"
    FontSize: 18px
    FontWeight: bold
    MarginBottom: 15px
    BorderBottom: 2px solid #007bff
    PaddingBottom: 10px
}

// Data table for checkbooks
tblChequiers is DataTable {
    Width: 100%
    Height: calc(100% - 50px)
    DataSource: lstChequiers
    Columns: [
        {Name: "Numéro", Width: 150},
        {Name: "Type", Width: 120},
        {Name: "État", Width: 100},
        {Name: "Plafond", Width: 120},
        {Name: "Actions", Width: 200}
    ]
    OnRowClick: SelectChequier
}

// Profile management section
ProfilsSection is Container {
    Width: 100%
    Height: 40%
    BackgroundColor: white
    Border: 1px solid #dee2e6
    BorderRadius: 8px
    Padding: 20px
}

ProfilsTitle is Static {
    Text: "Gestion des profils associés"
    FontSize: 18px
    FontWeight: bold
    MarginBottom: 15px
    BorderBottom: 2px solid #28a745
    PaddingBottom: 10px
}

// Search form
SearchForm is Container {
    Width: 100%
    Height: 60px
    MarginBottom: 15px
}

SearchLabel is Static {
    Text: "Rechercher par email:"
    Width: 150px
    VerticalAlign: middle
}

SearchInput is Edit {
    Width: 300px
    Placeholder: "Entrez l'email de l'utilisateur"
    DataSource: searchEmail
}

SearchButton is Button {
    Text: "Rechercher"
    Width: 100px
    BackgroundColor: #007bff
    Color: white
    OnClick: SearchUserByEmail
}

// Profile list
tblProfils is DataTable {
    Width: 100%
    Height: calc(100% - 120px)
    DataSource: lstProfils
    Columns: [
        {Name: "Email", Width: 250},
        {Name: "Nom", Width: 200},
        {Name: "État", Width: 100},
        {Name: "Actions", Width: 150}
    ]
}

// Action buttons
ActionButtons is Container {
    Width: 100%
    Height: 50px
    TextAlign: center
    MarginTop: 15px
}

AddProfilButton is Button {
    Text: "Ajouter Profil"
    Width: 120px
    BackgroundColor: #28a745
    Color: white
    OnClick: AddProfilToChequier
}

SuspendProfilButton is Button {
    Text: "Suspendre Profil"
    Width: 120px
    BackgroundColor: #dc3545
    Color: white
    OnClick: SuspendProfil
    MarginLeft: 10px
}

UpdateChequierButton is Button {
    Text: "Mettre à jour"
    Width: 120px
    BackgroundColor: #ffc107
    Color: black
    OnClick: UpdateChequierConfig
    MarginLeft: 10px
}

TABLE_CHEQUIERS is Table
{
    Columns=["Numéro","Type","État","Plafond"]
    DataSource=lstChequiers
    OnRowSelection=TABLE_CHEQUIERS_SelectRow
}
BTN_UPDATE is Button
{
    Text="Mettre à jour la configuration"
    OnClick=BTN_UPDATE_Click
    State=Grayed
}
EDT_EMAIL is Edit
{
    Placeholder="Adresse e-mail de l'utilisateur"
}
BTN_SEARCH is Button
{
    Text="Rechercher"
    OnClick=BTN_SEARCH_Click
}
BTN_ASSOCIATE is Button
{
    Text="Associer le profil"
    OnClick=BTN_ASSOCIATE_Click
    State=Grayed
}
TABLE_PROFILS is Table
{
    Columns=["Email","Nom","État"]
    DataSource=lstProfils
}
BTN_SUSPEND is Button
{
    Text="Suspendre le profil"
    OnClick=BTN_SUSPEND_Click
}
STC_INFO is Static
{
    Text=""
}
STC_ERROR is Static
{
    Text=""
}

[FUNCTIONS]
// Page initialization
PROCEDURE InitializePage()
    // Check if user is connected with valid profile
    IF gIDProfil <> 1 AND gIDProfil <> 3 THEN
        Error("Accès non autorisé. Profil requis: 1 ou 3")
        RETURN
    END
    
    // Load user's checkbooks
    LoadUserChequiers()
    
    // Load associated profiles
    LoadAssociatedProfils()

// Load user's checkbooks
PROCEDURE LoadUserChequiers()
    // In a real application, this would call an API
    // For demo purposes, we'll create sample data
    lstChequiers = []
    
    // Sample checkbook data
    chequier1 is Chequier
    chequier1.Numero = "0000159"
    chequier1.Type = "OTC"
    chequier1.Etat = "Actif"
    chequier1.Plafond = 50000
    lstChequiers.Add(chequier1)
    
    chequier2 is Chequier
    chequier2.Numero = "0000160"
    chequier2.Type = "Classique"
    chequier2.Etat = "Actif"
    chequier2.Plafond = 100000
    lstChequiers.Add(chequier2)

// Load associated profiles
PROCEDURE LoadAssociatedProfils()
    // In a real application, this would call an API
    // For demo purposes, we'll create sample data
    lstProfils = []
    
    // Sample profile data
    profil1 is Profil
    profil1.Email = "<EMAIL>"
    profil1.Nom = "Bibouth Lep"
    profil1.Etat = "Actif"
    lstProfils.Add(profil1)
    
    profil2 is Profil
    profil2.Email = "<EMAIL>"
    profil2.Nom = "Celine Bobo"
    profil2.Etat = "Suspendu"
    lstProfils.Add(profil2)

// Select a checkbook
PROCEDURE SelectChequier()
    selectedChequier = tblChequiers.SelectedRow
    Info("Chéquier sélectionné: " + selectedChequier.Numero)

// Search user by email
PROCEDURE SearchUserByEmail()
    IF searchEmail = "" THEN
        Error("Veuillez entrer une adresse email")
        RETURN
    END
    
    // In a real application, this would call an API
    // For demo purposes, we'll simulate the search
    IF searchEmail = "<EMAIL>" OR searchEmail = "<EMAIL>" THEN
        Info("Utilisateur trouvé: " + searchEmail)
        // Add to associated profiles list
        newProfil is Profil
        newProfil.Email = searchEmail
        newProfil.Nom = "Utilisateur trouvé"
        newProfil.Etat = "En attente"
        lstProfils.Add(newProfil)
    ELSE
        Error("Aucun utilisateur trouvé avec cet email")
    END

// Add profile to checkbook
PROCEDURE AddProfilToChequier()
    IF selectedChequier = NULL THEN
        Error("Veuillez sélectionner un chéquier")
        RETURN
    END
    
    IF searchEmail = "" THEN
        Error("Veuillez rechercher un utilisateur d'abord")
        RETURN
    END
    
    Info("Profil ajouté au chéquier: " + selectedChequier.Numero)

// Suspend profile
PROCEDURE SuspendProfil()
    selectedProfil is Profil = tblProfils.SelectedRow
    IF selectedProfil = NULL THEN
        Error("Veuillez sélectionner un profil")
        RETURN
    END
    
    selectedProfil.Etat = "Suspendu"
    Info("Profil suspendu: " + selectedProfil.Email)

// Update checkbook configuration
PROCEDURE UpdateChequierConfig()
    IF selectedChequier = NULL THEN
        Error("Veuillez sélectionner un chéquier")
        RETURN
    END
    
    Info("Configuration mise à jour pour le chéquier: " + selectedChequier.Numero)

[EVENTS]
// Page load event
OnPageLoad: InitializePage 

PROCEDURE TABLE_CHEQUIERS_SelectRow()
selectedChequier = TABLE_CHEQUIERS
BTN_UPDATE..State = Active

PROCEDURE BTN_SEARCH_Click()
searchEmail = EDT_EMAIL
IF searchEmail = "" THEN
    STC_ERROR = "Please enter an email address."
    RETURN
END
IF searchEmail ~= "<EMAIL>" OR searchEmail ~= "<EMAIL>" THEN
    STC_INFO = "User found: " + searchEmail
    bFound is bool = False
    FOR EACH profil OF lstProfils
        IF profil.Email = searchEmail THEN
            bFound = True
        END
    END
    IF NOT bFound THEN
        lstProfils.Add([{"Email":searchEmail,"Nom":"Utilisateur trouvé","Etat":"Actif"}])
        TABLE_PROFILS = lstProfils
    END
    BTN_ASSOCIATE..State = Active
ELSE
    STC_ERROR = "User not found or invalid email."
    BTN_ASSOCIATE..State = Grayed
END

PROCEDURE BTN_ASSOCIATE_Click()
IF selectedChequier = -1 THEN
    STC_ERROR = "Please select a checkbook."
    RETURN
END
STC_INFO = "Profile " + searchEmail + " associated to checkbook " + lstChequiers[selectedChequier].Numero

PROCEDURE BTN_UPDATE_Click()
IF selectedChequier = -1 THEN
    STC_ERROR = "Please select a checkbook."
    RETURN
END
STC_INFO = "Checkbook " + lstChequiers[selectedChequier].Numero + " updated."

PROCEDURE BTN_SUSPEND_Click()
nRow is int = TABLE_PROFILS
IF nRow = -1 THEN
    STC_ERROR = "Please select a profile."
    RETURN
END
lstProfils[nRow].Etat = "Suspendu"
TABLE_PROFILS = lstProfils
STC_INFO = "Profile suspended." 