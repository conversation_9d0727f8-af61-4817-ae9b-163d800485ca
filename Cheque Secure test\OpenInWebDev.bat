@echo off
echo ========================================
echo   ChèqueSecure WebDev Project Launcher
echo ========================================
echo.
echo Trying to open the project in WebDev...
echo.

REM Try different project file formats and WebDev versions
echo Attempting to open ChequeSecure.wdproj...
if exist "%~dp0ChequeSecure.wdproj" (
    start "" "C:\Program Files (x86)\PC SOFT\WebDev 28\WebDev.exe" "%~dp0ChequeSecure.wdproj"
    if %errorlevel% equ 0 goto success
)

echo Attempting to open ChequeSecure.wdp...
if exist "%~dp0ChequeSecure.wdp" (
    start "" "C:\Program Files (x86)\PC SOFT\WebDev 28\WebDev.exe" "%~dp0ChequeSecure.wdp"
    if %errorlevel% equ 0 goto success
)

echo Attempting to open ChequeSecure.wdc...
if exist "%~dp0ChequeSecure.wdc" (
    start "" "C:\Program Files (x86)\PC SOFT\WebDev 28\WebDev.exe" "%~dp0ChequeSecure.wdc"
    if %errorlevel% equ 0 goto success
)

REM Try alternative WebDev installation paths
echo Trying alternative WebDev installation paths...
start "" "C:\Program Files\PC SOFT\WebDev 28\WebDev.exe" "%~dp0ChequeSecure.wdproj"
if %errorlevel% equ 0 goto success

start "" "C:\Program Files (x86)\PC SOFT\WebDev 27\WebDev.exe" "%~dp0ChequeSecure.wdproj"
if %errorlevel% equ 0 goto success

start "" "C:\Program Files\PC SOFT\WebDev 27\WebDev.exe" "%~dp0ChequeSecure.wdproj"
if %errorlevel% equ 0 goto success

echo.
echo ========================================
echo   MANUAL INSTRUCTIONS
echo ========================================
echo WebDev not found in default locations.
echo.
echo Please manually open WebDev and try:
echo 1. File ^> Open Project
echo 2. Navigate to this folder: %~dp0
echo 3. Try opening these files in order:
echo    - ChequeSecure.wdproj
echo    - ChequeSecure.wdp
echo    - ChequeSecure.wdc
echo    - page_login.wdw (individual page)
echo.
echo If none work, your WebDev version might need
echo a different project file format.
echo.
pause
goto end

:success
echo.
echo Project opened successfully in WebDev!
echo.
echo LOGIN CREDENTIALS FOR TESTING:
echo ==============================
echo Quick Access (recommended):
echo - Profile 1: <EMAIL> / admin123
echo - Profile 3: <EMAIL> / user123
echo.
echo Test Users:
echo - <EMAIL> / test123
echo - <EMAIL> / test123
echo.
pause

:end