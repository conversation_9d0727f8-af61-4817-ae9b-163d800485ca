@echo off
echo Opening ChequeSecure project in WebDev...
echo.
echo If WebDev is installed in the default location, this will open the project.
echo If not, please manually open ChequeSecure.wdc in WebDev.
echo.

REM Try to open with WebDev if it's in the default location
start "" "C:\Program Files (x86)\PC SOFT\WebDev 28\WebDev.exe" "%~dp0ChequeSecure.wdc"

REM Alternative: try different WebDev versions
if not exist "C:\Program Files (x86)\PC SOFT\WebDev 28\WebDev.exe" (
    start "" "C:\Program Files\PC SOFT\WebDev 28\WebDev.exe" "%~dp0ChequeSecure.wdc"
)

if not exist "C:\Program Files\PC SOFT\WebDev 28\WebDev.exe" (
    echo WebDev not found in default locations.
    echo Please manually open ChequeSecure.wdc in WebDev.
    pause
) 