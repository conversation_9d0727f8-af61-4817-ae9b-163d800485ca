// ========================================
// PAGE_CHEQUIERS - Copy this code into your checkbook management page
// ========================================

// ===== VARIABLES =====
lstChequiers is array of Chequier
lstProfils is array of Profil
selectedChequier is Chequier
selectedProfilIndex is int = -1
searchEmail is string

// ===== CONTROLS TO CREATE =====
// Create these controls in WebDev page designer:
// 1. TABLE_CHEQUIERS (Table control)
// 2. BTN_UPDATE (Button)
// 3. EDT_EMAIL (Edit control)
// 4. BTN_SEARCH (Button)
// 5. BTN_ASSOCIATE (Button)
// 6. TABLE_PROFILS (Table control)
// 7. BTN_SUSPEND (Button)
// 8. STC_INFO (Static control)
// 9. STC_ERROR (Static control)

// ===== PAGE INITIALIZATION =====
PROCEDURE InitializePage()
	// Check if user is connected with valid profile
	IF gIDProfil <> 1 AND gIDProfil <> 3 THEN
		STC_ERROR = "Accès non autorisé. Profil requis: 1 ou 3"
		RETURN
	END
	
	// Load user's checkbooks
	LoadUserChequiers()
	
	// Load associated profiles
	LoadAssociatedProfils()
	
	STC_INFO = "Page chargée avec succès"
END

// ===== LOAD DATA =====
PROCEDURE LoadUserChequiers()
	// Clear existing data
	TableDeleteAll(TABLE_CHEQUIERS)
	lstChequiers = []
	
	// Sample checkbook data for testing
	chequier1 is Chequier
	chequier1.Numero = "0000159"
	chequier1.Type = "OTC"
	chequier1.Etat = "Actif"
	chequier1.Plafond = 50000
	chequier1.DateCreation = "01/01/2024"
	chequier1.IDProfil = gIDProfil
	lstChequiers.Add(chequier1)
	
	chequier2 is Chequier
	chequier2.Numero = "0000160"
	chequier2.Type = "Classique"
	chequier2.Etat = "Actif"
	chequier2.Plafond = 100000
	chequier2.DateCreation = "15/01/2024"
	chequier2.IDProfil = gIDProfil
	lstChequiers.Add(chequier2)
	
	chequier3 is Chequier
	chequier3.Numero = "0000161"
	chequier3.Type = "Prépayé"
	chequier3.Etat = "Actif"
	chequier3.Plafond = 25000
	chequier3.DateCreation = "20/01/2024"
	chequier3.IDProfil = gIDProfil
	lstChequiers.Add(chequier3)
	
	// Add to table
	FOR i = 1 TO lstChequiers..Count
		TableAddLine(TABLE_CHEQUIERS, lstChequiers[i].Numero, lstChequiers[i].Type, lstChequiers[i].Etat, lstChequiers[i].Plafond, lstChequiers[i].DateCreation)
	END
	
	STC_INFO = "Chéquiers chargés: " + lstChequiers..Count + " éléments"
END

PROCEDURE LoadAssociatedProfils()
	// Clear existing data
	TableDeleteAll(TABLE_PROFILS)
	lstProfils = []
	
	// Sample profile data for testing
	profil1 is Profil
	profil1.IDProfil = 1
	profil1.Email = "<EMAIL>"
	profil1.Nom = "Lepine"
	profil1.Prenom = "Bibouth"
	profil1.Etat = "Actif"
	profil1.DateCreation = "01/01/2024"
	lstProfils.Add(profil1)
	
	profil2 is Profil
	profil2.IDProfil = 3
	profil2.Email = "<EMAIL>"
	profil2.Nom = "Bobo"
	profil2.Prenom = "Celine"
	profil2.Etat = "Actif"
	profil2.DateCreation = "05/01/2024"
	lstProfils.Add(profil2)
	
	// Add to table
	FOR i = 1 TO lstProfils..Count
		TableAddLine(TABLE_PROFILS, lstProfils[i].Email, lstProfils[i].Nom + " " + lstProfils[i].Prenom, lstProfils[i].Etat)
	END
END

// ===== EVENT HANDLERS =====
PROCEDURE TABLE_CHEQUIERS_SelectRow()
	selectedRow is int = TABLE_CHEQUIERS
	IF selectedRow > 0 THEN
		selectedChequier = lstChequiers[selectedRow]
		BTN_UPDATE..State = Active
		STC_INFO = "Chéquier sélectionné: " + selectedChequier.Numero
	END
END

PROCEDURE BTN_UPDATE_Click()
	IF selectedChequier.Numero = "" THEN
		STC_ERROR = "Veuillez sélectionner un chéquier"
		RETURN
	END
	
	STC_INFO = "Configuration mise à jour pour le chéquier: " + selectedChequier.Numero
	Info("Configuration mise à jour avec succès!")
END

PROCEDURE BTN_SEARCH_Click()
	email is string = EDT_EMAIL
	STC_ERROR = ""
	STC_INFO = ""
	
	IF Length(email) = 0 THEN
		STC_ERROR = "Veuillez saisir une adresse e-mail"
		RETURN
	END
	
	// Check against known test emails
	IF email = "<EMAIL>" OR email = "<EMAIL>" THEN
		STC_INFO = "Utilisateur trouvé: " + email
		BTN_ASSOCIATE..State = Active
		searchEmail = email
	ELSE
		STC_ERROR = "Utilisateur non trouvé dans la base de données: " + email
		BTN_ASSOCIATE..State = Grayed
	END
END

PROCEDURE BTN_ASSOCIATE_Click()
	IF Length(searchEmail) = 0 THEN
		STC_ERROR = "Aucun utilisateur sélectionné pour l'association"
		RETURN
	END
	
	// Check if profile is already associated
	FOR i = 1 TO lstProfils..Count
		IF lstProfils[i].Email = searchEmail THEN
			STC_ERROR = "Ce profil est déjà associé à ce chéquier"
			RETURN
		END
	END
	
	// Add new profile
	newProfil is Profil
	newProfil.Email = searchEmail
	newProfil.Nom = "Nouveau"
	newProfil.Prenom = "Utilisateur"
	newProfil.Etat = "Actif"
	newProfil.DateCreation = DateSys()
	lstProfils.Add(newProfil)
	
	// Add to table
	TableAddLine(TABLE_PROFILS, newProfil.Email, newProfil.Nom + " " + newProfil.Prenom, newProfil.Etat)
	
	STC_INFO = "Profil associé avec succès: " + searchEmail
	
	// Reset form
	EDT_EMAIL = ""
	searchEmail = ""
	BTN_ASSOCIATE..State = Grayed
END

PROCEDURE TABLE_PROFILS_SelectRow()
	selectedProfilIndex = TABLE_PROFILS
	IF selectedProfilIndex > 0 THEN
		BTN_SUSPEND..State = Active
		STC_INFO = "Profil sélectionné: " + lstProfils[selectedProfilIndex].Email
	END
END

PROCEDURE BTN_SUSPEND_Click()
	IF selectedProfilIndex <= 0 THEN
		STC_ERROR = "Veuillez sélectionner un profil"
		RETURN
	END
	
	// Suspend the selected profile
	lstProfils[selectedProfilIndex].Etat = "Suspendu"
	
	// Update table
	TABLE_PROFILS[selectedProfilIndex, 3] = "Suspendu"
	
	STC_INFO = "Profil suspendu: " + lstProfils[selectedProfilIndex].Email
	
	// Reset selection
	selectedProfilIndex = -1
	BTN_SUSPEND..State = Grayed
END

// ===== EVENTS TO SET =====
// In WebDev, set these events:
// - Page OnLoad: InitializePage
// - TABLE_CHEQUIERS OnRowSelection: TABLE_CHEQUIERS_SelectRow
// - BTN_UPDATE OnClick: BTN_UPDATE_Click
// - BTN_SEARCH OnClick: BTN_SEARCH_Click
// - BTN_ASSOCIATE OnClick: BTN_ASSOCIATE_Click
// - TABLE_PROFILS OnRowSelection: TABLE_PROFILS_SelectRow
// - BTN_SUSPEND OnClick: BTN_SUSPEND_Click

// ===== TABLE COLUMNS =====
// TABLE_CHEQUIERS columns: Numéro, Type, État, Plafond, Date Création
// TABLE_PROFILS columns: Email, Nom, État
