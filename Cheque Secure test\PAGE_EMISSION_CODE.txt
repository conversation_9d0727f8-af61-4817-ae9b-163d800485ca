// ========================================
// PAGE_EMISSION - Copy this code into your check emission page
// ========================================

// ===== VARIABLES =====
bHasClassicCheckbook is boolean = False
bHasPrepaidCheckbook is boolean = False

// ===== CONTROLS TO CREATE =====
// Create these controls in WebDev page designer:
// 1. STC_TITLE (Static control)
// 2. STC_USER_INFO (Static control)
// 3. BTN_RETRAIT (Button)
// 4. BTN_PAIEMENT (Button)
// 5. BTN_CLASSIQUE (Button)
// 6. BTN_PREPAYE (Button)
// 7. STC_INFO (Static control)
// 8. STC_ERROR (Static control)

// ===== PAGE INITIALIZATION =====
PROCEDURE InitializePage()
	// Check if user is connected with valid profile
	IF gIDProfil <> 1 AND gIDProfil <> 3 THEN
		STC_ERROR = "Accès non autorisé. Profil requis: 1 ou 3"
		// Disable all buttons
		BTN_RETRAIT..State = Grayed
		BTN_PAIEMENT..State = Grayed
		BTN_CLASSIQUE..State = Grayed
		BTN_PREPAYE..State = Grayed
		RETURN
	END
	
	// Update user info display
	STC_TITLE = "Émission de Chèques"
	STC_USER_INFO = "Utilisateur connecté - Profil ID: " + gIDProfil + " | Email: " + gUserEmail
	
	// Check checkbook configurations
	CheckChequierConfigurations()
	
	// Show welcome message
	STC_INFO = "Bienvenue dans le module d'émission de chèques"
END

// ===== CHECK CONFIGURATIONS =====
PROCEDURE CheckChequierConfigurations()
	// In a real application, this would call an API to check user's checkbooks
	// For demo purposes, we'll assume user has access based on profile
	
	IF gIDProfil = 1 OR gIDProfil = 3 THEN
		bHasClassicCheckbook = True
		bHasPrepaidCheckbook = True
		STC_INFO = STC_INFO + " - Accès autorisé aux chéquiers classiques et prépayés"
	ELSE
		bHasClassicCheckbook = False
		bHasPrepaidCheckbook = False
		STC_ERROR = "Accès limité - Contactez votre administrateur"
		BTN_CLASSIQUE..State = Grayed
		BTN_PREPAYE..State = Grayed
	END
END

// ===== BUTTON CLICK HANDLERS =====
PROCEDURE BTN_RETRAIT_Click()
	STC_ERROR = ""
	STC_INFO = "Redirection vers la page de retrait OTC..."
	
	// Set operation type for the transaction page
	gOperationType = "RETRAIT"
	PageDisplay(PAGE_TRANSACTION)
END

PROCEDURE BTN_PAIEMENT_Click()
	STC_ERROR = ""
	STC_INFO = "Redirection vers la page de paiement OTC..."
	
	// Set operation type for the transaction page
	gOperationType = "PAIEMENT"
	PageDisplay(PAGE_TRANSACTION)
END

PROCEDURE BTN_CLASSIQUE_Click()
	STC_ERROR = ""
	
	IF NOT bHasClassicCheckbook THEN
		STC_ERROR = "Vous n'avez pas accès aux chéquiers classiques"
		RETURN
	END
	
	STC_INFO = "Émission de chèque classique sélectionnée"
	Info("Fonctionnalité d'émission de chèque classique - À implémenter")
END

PROCEDURE BTN_PREPAYE_Click()
	STC_ERROR = ""
	
	IF NOT bHasPrepaidCheckbook THEN
		STC_ERROR = "Vous n'avez pas accès aux chéquiers prépayés"
		RETURN
	END
	
	STC_INFO = "Émission de chèque prépayé sélectionnée"
	Info("Fonctionnalité d'émission de chèque prépayé - À implémenter")
END

// ===== EVENTS TO SET =====
// In WebDev, set these events:
// - Page OnLoad: InitializePage
// - BTN_RETRAIT OnClick: BTN_RETRAIT_Click
// - BTN_PAIEMENT OnClick: BTN_PAIEMENT_Click
// - BTN_CLASSIQUE OnClick: BTN_CLASSIQUE_Click
// - BTN_PREPAYE OnClick: BTN_PREPAYE_Click

// ===== CONTROL PROPERTIES =====
// Set these properties in WebDev:
// STC_TITLE: FontSize=20, FontBold=True, Color=Blue, TextAlign=Center
// STC_USER_INFO: FontSize=12, Color=Gray, BackgroundColor=LightGray
// BTN_RETRAIT: Width=200, Height=50, Text="Retrait par chèque OTC", BackgroundColor=Blue, Color=White
// BTN_PAIEMENT: Width=200, Height=50, Text="Paiement par chèque OTC", BackgroundColor=Cyan, Color=White
// BTN_CLASSIQUE: Width=200, Height=50, Text="Émission de chèque classique", BackgroundColor=Green, Color=White
// BTN_PREPAYE: Width=200, Height=50, Text="Émission de chèque prépayé", BackgroundColor=Orange, Color=White
// STC_INFO: Color=Green, FontBold=True
// STC_ERROR: Color=Red, FontBold=True

// ===== LAYOUT SUGGESTIONS =====
// Arrange controls:
// 1. STC_TITLE (top, centered)
// 2. STC_USER_INFO (below title)
// 3. OTC Operations section:
//    - BTN_RETRAIT and BTN_PAIEMENT (side by side)
// 4. Standard Operations section:
//    - BTN_CLASSIQUE and BTN_PREPAYE (side by side)
// 5. STC_INFO and STC_ERROR (bottom)
