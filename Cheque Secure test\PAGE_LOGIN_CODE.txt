// ========================================
// PAGE_LOGIN - Copy this code into your login page
// ========================================

// ===== VARIABLES =====
loginAttempts is int = 0
maxLoginAttempts is int = 3

// ===== CONTROLS TO CREATE =====
// Create these controls in WebDev page designer:
// 1. COMBO_PROFILE (Combo box)
// 2. EDT_EMAIL (Edit control)
// 3. EDT_PASSWORD (Edit control, type = password)
// 4. BTN_LOGIN (Button)
// 5. BTN_QUICK_PROFILE1 (Button)
// 6. BTN_QUICK_PROFILE3 (Button)
// 7. STC_INFO (Static control)
// 8. STC_ERROR (Static control)

// ===== PAGE INITIALIZATION =====
PROCEDURE InitializePage()
	gIsConnected = False
	gIDProfil = 0
	gUserEmail = ""
	gUserName = ""
	
	// Set combo content
	ListAdd(COMBO_PROFILE, "Sélectionner un profil")
	ListAdd(COMBO_PROFILE, "Profil 1 - Administrateur")
	ListAdd(COMBO_PROFILE, "Profil 3 - Utilisateur Standard")
	
	STC_INFO = "Veuillez vous connecter pour accéder au système"
	STC_ERROR = ""
END

// ===== LOGIN BUTTON =====
PROCEDURE BTN_LOGIN_Click()
	STC_ERROR = ""
	STC_INFO = ""
	
	selectedProfile is int = COMBO_PROFILE
	email is string = EDT_EMAIL
	password is string = EDT_PASSWORD
	
	IF selectedProfile <= 1 THEN
		STC_ERROR = "Veuillez sélectionner un profil"
		RETURN
	END
	
	IF Length(email) = 0 THEN
		STC_ERROR = "Veuillez saisir votre adresse e-mail"
		RETURN
	END
	
	IF Length(password) = 0 THEN
		STC_ERROR = "Veuillez saisir votre mot de passe"
		RETURN
	END
	
	IF AuthenticateUser(selectedProfile, email, password) THEN
		STC_INFO = "Connexion réussie! Redirection en cours..."
		Multitask(1000)
		PageDisplay(PAGE_MAIN)
	ELSE
		loginAttempts = loginAttempts + 1
		STC_ERROR = "Identifiants incorrects (Tentative " + loginAttempts + "/" + maxLoginAttempts + ")"
		
		IF loginAttempts >= maxLoginAttempts THEN
			STC_ERROR = "Trop de tentatives de connexion. Accès bloqué."
			BTN_LOGIN..State = Grayed
		END
	END
END

// ===== QUICK ACCESS BUTTONS =====
PROCEDURE BTN_QUICK_PROFILE1_Click()
	COMBO_PROFILE = 2  // Profile 1
	EDT_EMAIL = "<EMAIL>"
	EDT_PASSWORD = "admin123"
	BTN_LOGIN_Click()
END

PROCEDURE BTN_QUICK_PROFILE3_Click()
	COMBO_PROFILE = 3  // Profile 3
	EDT_EMAIL = "<EMAIL>"
	EDT_PASSWORD = "user123"
	BTN_LOGIN_Click()
END

// ===== AUTHENTICATION FUNCTION =====
FUNCTION AuthenticateUser(profileIndex is int, email is string, password is string) is boolean
	IF profileIndex = 2 AND email = "<EMAIL>" AND password = "admin123" THEN
		gIDProfil = 1
		gUserEmail = email
		gUserName = "Administrateur"
		gIsConnected = True
		RESULT True
	ELSE IF profileIndex = 3 AND email = "<EMAIL>" AND password = "user123" THEN
		gIDProfil = 3
		gUserEmail = email
		gUserName = "Utilisateur Standard"
		gIsConnected = True
		RESULT True
	ELSE IF email = "<EMAIL>" AND password = "test123" THEN
		gIDProfil = 1
		gUserEmail = email
		gUserName = "Bibouth Lep"
		gIsConnected = True
		RESULT True
	ELSE IF email = "<EMAIL>" AND password = "test123" THEN
		gIDProfil = 3
		gUserEmail = email
		gUserName = "Celine Bobo"
		gIsConnected = True
		RESULT True
	END
	
	RESULT False
END

// ===== EVENTS TO SET =====
// In WebDev, set these events:
// - Page OnLoad: InitializePage
// - BTN_LOGIN OnClick: BTN_LOGIN_Click
// - BTN_QUICK_PROFILE1 OnClick: BTN_QUICK_PROFILE1_Click
// - BTN_QUICK_PROFILE3 OnClick: BTN_QUICK_PROFILE3_Click

// ===== CONTROL PROPERTIES =====
// Set these properties in WebDev:
// COMBO_PROFILE: Width=300, Height=30
// EDT_EMAIL: Width=300, Height=30, Placeholder="Votre adresse e-mail"
// EDT_PASSWORD: Width=300, Height=30, Type=Password, Placeholder="Votre mot de passe"
// BTN_LOGIN: Width=300, Height=40, Text="Se connecter", BackgroundColor=Blue
// BTN_QUICK_PROFILE1: Width=140, Height=30, Text="Profil 1 (Admin)", BackgroundColor=Green
// BTN_QUICK_PROFILE3: Width=140, Height=30, Text="Profil 3 (User)", BackgroundColor=Cyan
// STC_INFO: Color=Green, FontBold=True
// STC_ERROR: Color=Red, FontBold=True
