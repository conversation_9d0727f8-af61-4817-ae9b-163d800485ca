// ========================================
// PAGE_MAIN - Copy this code into your main page
// ========================================

// ===== CONTROLS TO CREATE =====
// Create these controls in WebDev page designer:
// 1. STC_WELCOME (Static control)
// 2. STC_USER_INFO (Static control)
// 3. BTN_GESTION (Button)
// 4. BTN_EMISSION (Button)
// 5. STC_INFO (Static control)

// ===== PAGE INITIALIZATION =====
PROCEDURE InitializePage()
	// Check if user is connected
	IF NOT gIsConnected THEN
		Error("Veuillez vous connecter pour accéder à l'application")
		PageDisplay(PAGE_LOGIN)
		RETURN
	END
	
	// Update welcome message
	STC_WELCOME = "Bienvenue dans ChèqueSecure"
	STC_USER_INFO = "Utilisateur connecté - Profil ID: " + gIDProfil + " | Email: " + gUserEmail
	STC_INFO = "Choisissez un module pour commencer"
END

// ===== NAVIGATION BUTTONS =====
PROCEDURE BTN_GESTION_Click()
	STC_INFO = "Redirection vers la gestion des chéquiers..."
	PageDisplay(PAGE_CHEQUIERS)
END

PROCEDURE BTN_EMISSION_Click()
	STC_INFO = "Redirection vers l'émission de chèques..."
	PageDisplay(PAGE_EMISSION)
END

// ===== EVENTS TO SET =====
// In WebDev, set these events:
// - Page OnLoad: InitializePage
// - BTN_GESTION OnClick: BTN_GESTION_Click
// - BTN_EMISSION OnClick: BTN_EMISSION_Click

// ===== CONTROL PROPERTIES =====
// Set these properties in WebDev:
// STC_WELCOME: FontSize=24, FontBold=True, Color=Blue, TextAlign=Center
// STC_USER_INFO: FontSize=12, Color=Gray, BackgroundColor=LightGray, Padding=10
// BTN_GESTION: Width=200, Height=60, Text="Gestion des Chéquiers", BackgroundColor=Blue, Color=White
// BTN_EMISSION: Width=200, Height=60, Text="Émission de Chèques", BackgroundColor=Green, Color=White
// STC_INFO: Color=Blue, FontBold=True, TextAlign=Center

// ===== LAYOUT SUGGESTIONS =====
// Arrange controls vertically:
// 1. STC_WELCOME (top, centered)
// 2. STC_USER_INFO (below welcome)
// 3. BTN_GESTION and BTN_EMISSION (side by side, centered)
// 4. STC_INFO (bottom, centered)
