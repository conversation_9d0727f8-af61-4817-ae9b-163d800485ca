// ========================================
// PAGE_TRANSACTION - Copy this code into your OTC transaction page
// ========================================

// ===== VARIABLES =====
currentOperation is string = "RETRAIT"
selectedCheque is ChequeInfo
userBankAccounts is array of CompteClient
selectedAccountIndex is int = -1
transactionAmount is currency = 0
bChequeValidated is boolean = False

// ===== CONTROLS TO CREATE =====
// Create these controls in WebDev page designer:
// 1. STC_TITLE (Static control)
// 2. STC_OPERATION_TYPE (Static control)
// 3. EDT_CHEQUE_NUMBER (Edit control)
// 4. BTN_GET_CHEQUE (Button)
// 5. EDT_REF (Edit control)
// 6. BTN_SEARCH_REF (Button)
// 7. STC_CHEQUE_DETAILS (Static control)
// 8. COMBO_BANK_ACCOUNT (Combo control)
// 9. BTN_LOAD_ACCOUNTS (Button)
// 10. EDT_AMOUNT (Edit control, type=Currency)
// 11. BTN_CONFIRM (Button)
// 12. BTN_CANCEL (Button)
// 13. STC_RESULT (Static control)
// 14. STC_ERROR (Static control)

// ===== PAGE INITIALIZATION =====
PROCEDURE InitializePage()
	// Determine operation type from global variable
	IF gOperationType <> "" THEN
		currentOperation = gOperationType
	ELSE
		currentOperation = "RETRAIT"
	END
	
	// Update UI based on operation type
	UpdateUIForOperation()
	
	// Show welcome message
	STC_RESULT = "Veuillez valider un chèque pour commencer la transaction"
END

// ===== UPDATE UI FOR OPERATION =====
PROCEDURE UpdateUIForOperation()
	IF currentOperation = "RETRAIT" THEN
		STC_TITLE = "Transaction OTC - Retrait"
		STC_OPERATION_TYPE = "Retrait dans un compte bancaire via le chéquier OTC de l'institution"
		EDT_REF..Visible = False
		BTN_SEARCH_REF..Visible = False
	ELSE IF currentOperation = "PAIEMENT" THEN
		STC_TITLE = "Transaction OTC - Paiement"
		STC_OPERATION_TYPE = "Paiement d'un reçu ou facture via le chéquier OTC du magasin ou de la boutique"
		EDT_REF..Visible = True
		BTN_SEARCH_REF..Visible = True
	END
END

// ===== VALIDATE CHEQUE =====
PROCEDURE BTN_GET_CHEQUE_Click()
	chequeNumber is string = EDT_CHEQUE_NUMBER
	STC_ERROR = ""
	STC_RESULT = ""
	
	IF Length(chequeNumber) = 0 THEN
		STC_ERROR = "Veuillez saisir un numéro de chèque"
		RETURN
	END
	
	// Call API to get cheque information
	STC_RESULT = "Validation du chèque en cours..."
	chequeData is string = GetValidCheque(chequeNumber)
	
	IF Length(chequeData) = 0 THEN
		STC_ERROR = "Erreur lors de la récupération des données du chèque"
		RETURN
	END
	
	// Parse the JSON response (simplified)
	selectedCheque = ParseChequeJSON(chequeData)
	
	// Validate cheque for transaction
	IF NOT ValidateChequeForTransaction(selectedCheque) THEN
		STC_ERROR = "Ce chèque ne peut pas être utilisé (Activé, Signalé ou Réservé)"
		STC_CHEQUE_DETAILS = "Chèque: " + selectedCheque.Numero + " - Statut: Non valide pour transaction"
		RETURN
	END
	
	// Check if it's an OTC cheque for payment operations
	IF currentOperation = "PAIEMENT" AND selectedCheque.Type <> "OTC" THEN
		STC_ERROR = "Seuls les chèques OTC sont autorisés pour les paiements"
		RETURN
	END
	
	// Display cheque details
	STC_CHEQUE_DETAILS = "Chèque: " + selectedCheque.Numero + " - Type: " + selectedCheque.Type + " - Statut: Valide"
	bChequeValidated = True
	STC_RESULT = "Chèque validé avec succès"
	
	// Show next step based on operation type
	IF currentOperation = "RETRAIT" THEN
		COMBO_BANK_ACCOUNT..Visible = True
		BTN_LOAD_ACCOUNTS..Visible = True
		STC_RESULT = STC_RESULT + " - Veuillez charger vos comptes bancaires"
	ELSE IF currentOperation = "PAIEMENT" THEN
		EDT_AMOUNT..Visible = True
		STC_RESULT = STC_RESULT + " - Veuillez saisir le montant"
	END
END

// ===== SEARCH BY REFERENCE =====
PROCEDURE BTN_SEARCH_REF_Click()
	ref is string = EDT_REF
	STC_ERROR = ""
	STC_RESULT = ""
	
	IF Length(ref) = 0 THEN
		STC_ERROR = "Veuillez saisir une référence"
		RETURN
	END
	
	// Call API to search cheque by reference
	STC_RESULT = "Recherche en cours..."
	chequeData is string = SearchChequeByReference(ref)
	
	IF Length(chequeData) = 0 THEN
		STC_ERROR = "Aucun chèque trouvé pour cette référence"
		RETURN
	END
	
	// Parse and validate the cheque
	selectedCheque = ParseChequeJSON(chequeData)
	
	IF NOT ValidateChequeForTransaction(selectedCheque) THEN
		STC_ERROR = "Le chèque trouvé ne peut pas être utilisé"
		RETURN
	END
	
	IF selectedCheque.Type <> "OTC" THEN
		STC_ERROR = "Seuls les chèques OTC sont autorisés pour les paiements"
		RETURN
	END
	
	// Update cheque number field and display details
	EDT_CHEQUE_NUMBER = selectedCheque.Numero
	STC_CHEQUE_DETAILS = "Chèque: " + selectedCheque.Numero + " - Référence: " + ref + " - Type: " + selectedCheque.Type
	bChequeValidated = True
	EDT_AMOUNT..Visible = True
	STC_RESULT = "Chèque trouvé et validé - Veuillez saisir le montant"
END

// ===== LOAD BANK ACCOUNTS =====
PROCEDURE BTN_LOAD_ACCOUNTS_Click()
	STC_ERROR = ""
	STC_RESULT = "Chargement des comptes bancaires..."
	
	// Call API to get user bank accounts
	accountsData is string = GetUserBankAccounts(gIDProfil)
	
	IF Length(accountsData) = 0 THEN
		STC_ERROR = "Erreur lors du chargement des comptes bancaires"
		RETURN
	END
	
	// Clear and populate combo box with sample data
	ListDeleteAll(COMBO_BANK_ACCOUNT)
	ListAdd(COMBO_BANK_ACCOUNT, "Sélectionner un compte")
	ListAdd(COMBO_BANK_ACCOUNT, "********** - Compte Courant (150,000 FCFA)")
	ListAdd(COMBO_BANK_ACCOUNT, "********** - Compte Épargne (75,000 FCFA)")
	
	STC_RESULT = "Comptes bancaires chargés - Veuillez sélectionner un compte"
	EDT_AMOUNT..Visible = True
END

// ===== HANDLE ACCOUNT SELECTION =====
PROCEDURE COMBO_BANK_ACCOUNT_Change()
	selectedAccountIndex = COMBO_BANK_ACCOUNT
	IF selectedAccountIndex > 1 THEN
		STC_RESULT = "Compte sélectionné - Veuillez saisir le montant"
		BTN_CONFIRM..Visible = True
	END
END

// ===== CONFIRM TRANSACTION =====
PROCEDURE BTN_CONFIRM_Click()
	transactionAmount = EDT_AMOUNT
	
	IF transactionAmount <= 0 THEN
		STC_ERROR = "Veuillez saisir un montant valide"
		RETURN
	END
	
	IF currentOperation = "RETRAIT" AND selectedAccountIndex <= 1 THEN
		STC_ERROR = "Veuillez sélectionner un compte bancaire"
		RETURN
	END
	
	// Confirm transaction
	result is int = YesNo("Confirmer la transaction de " + transactionAmount + " FCFA?")
	IF result = Yes THEN
		STC_ERROR = ""
		STC_RESULT = "Traitement de la transaction en cours..."
		
		// Simulate processing
		Multitask(2000)
		
		// Generate transaction ID
		transactionID is string = "TXN" + DateToString(DateSys(), "YYYYMMDD") + TimeToString(TimeSys(), "HHMMSS")
		
		STC_RESULT = "Transaction réussie!" + CR + "ID Transaction: " + transactionID + CR + "Montant: " + transactionAmount + " FCFA"
		
		BTN_CONFIRM..State = Grayed
		BTN_CONFIRM..Text = "Transaction terminée"
		
		Info("Transaction réussie! ID: " + transactionID)
	END
END

// ===== CANCEL TRANSACTION =====
PROCEDURE BTN_CANCEL_Click()
	result is int = YesNo("Êtes-vous sûr de vouloir annuler cette transaction?")
	IF result = Yes THEN
		// Reset form
		EDT_CHEQUE_NUMBER = ""
		EDT_REF = ""
		EDT_AMOUNT = 0
		COMBO_BANK_ACCOUNT = 1
		
		// Reset variables
		bChequeValidated = False
		selectedAccountIndex = -1
		transactionAmount = 0
		
		STC_RESULT = "Transaction annulée"
		STC_ERROR = ""
		STC_CHEQUE_DETAILS = ""
		
		// Hide controls
		COMBO_BANK_ACCOUNT..Visible = False
		BTN_LOAD_ACCOUNTS..Visible = False
		EDT_AMOUNT..Visible = False
		BTN_CONFIRM..Visible = False
	END
END

// ===== EVENTS TO SET =====
// In WebDev, set these events:
// - Page OnLoad: InitializePage
// - BTN_GET_CHEQUE OnClick: BTN_GET_CHEQUE_Click
// - BTN_SEARCH_REF OnClick: BTN_SEARCH_REF_Click
// - BTN_LOAD_ACCOUNTS OnClick: BTN_LOAD_ACCOUNTS_Click
// - COMBO_BANK_ACCOUNT OnChange: COMBO_BANK_ACCOUNT_Change
// - BTN_CONFIRM OnClick: BTN_CONFIRM_Click
// - BTN_CANCEL OnClick: BTN_CANCEL_Click

// ===== CONTROL PROPERTIES =====
// Set these properties in WebDev:
// STC_TITLE: FontSize=18, FontBold=True, Color=Blue
// EDT_CHEQUE_NUMBER: Width=200, Height=30, Placeholder="Ex: 0000159"
// BTN_GET_CHEQUE: Width=150, Height=30, Text="Valider le chèque", BackgroundColor=Blue
// EDT_REF: Width=200, Height=30, Placeholder="Référence", Visible=False initially
// BTN_SEARCH_REF: Width=150, Height=30, Text="Rechercher", BackgroundColor=Cyan, Visible=False initially
// COMBO_BANK_ACCOUNT: Width=300, Height=30, Visible=False initially
// BTN_LOAD_ACCOUNTS: Width=150, Height=30, Text="Charger les comptes", BackgroundColor=Green, Visible=False initially
// EDT_AMOUNT: Width=200, Height=30, Type=Currency, Visible=False initially
// BTN_CONFIRM: Width=200, Height=40, Text="Confirmer la transaction", BackgroundColor=Green, Visible=False initially
// BTN_CANCEL: Width=120, Height=40, Text="Annuler", BackgroundColor=Gray
// STC_RESULT: Color=Green, FontBold=True
// STC_ERROR: Color=Red, FontBold=True
