CHEQUESECURE - <PERSON><PERSON><PERSON><PERSON>V PROJECT
==============================

This is a complete WebDev project for the ChèqueSecure application.
The project includes all the pages and functionality specified in the French documentation.

🚀 QUICK START:
===============
1. Double-click on "OpenInWebDev.bat" to automatically open the project
   OR
2. Open WebDev and load "ChequeSecure.wdc" directly
   OR
3. Open WebDev and load "ChequeSecure.wdcproj" if your WebDev version supports it

📁 PROJECT STRUCTURE:
=====================
Main Files:
- ChequeSecure.wdcproj (Main project file)
- ChequeSecure.wdc (Project configuration with data structures and API functions)

Pages:
- page_login.wdc (Login/Authentication page)
- page_main.wdc (Main dashboard/navigation page)
- profilsOTC/page_chequiers.wdc (Checkbook management - "Vos Chéquiers")
- page_emission/page_chequeEmission.wdc (Check emission selection page)
- emission/PAGE_transactionchequeRetraitOTC.wdc (OTC transaction page)

🔐 AUTHENTICATION & TESTING:
============================
Login Credentials for Testing:

Quick Access Buttons (recommended):
- Profile 1 (Admin): <EMAIL> / admin123
- Profile 3 (User): <EMAIL> / user123

Test Users from Specifications:
- <EMAIL> / test123 (Profile 1)
- <EMAIL> / test123 (Profile 3)

Profile Access Levels:
- Profile 1: Full access to all checkbook types (Classic, Prepaid, OTC)
- Profile 3: Standard user access to all checkbook types
- Other profiles: Limited access (for testing access control)

🧪 TESTING SCENARIOS:
====================

1. LOGIN TESTING:
   - Test with valid credentials (should redirect to main page)
   - Test with invalid credentials (should show error)
   - Test profile-based access control

2. CHECKBOOK MANAGEMENT (page_chequiers):
   - View list of user's checkbooks
   - Select a checkbook to view/edit configuration
   - Search for users by email (<EMAIL>, <EMAIL>)
   - Associate new profiles to checkbooks
   - Suspend existing profiles

3. CHECK EMISSION (page_chequeEmission):
   - Access four types of operations:
     * Retrait par chèque OTC (redirects to transaction page)
     * Paiement par chèque OTC (redirects to transaction page)
     * Émission de chèque classique (shows info message)
     * Émission de chèque prépayé (shows info message)

4. OTC TRANSACTIONS (PAGE_transactionchequeRetraitOTC):
   - Test withdrawal operations:
     * Validate cheque number (try: 0000159)
     * Load bank accounts
     * Enter amount and confirm transaction
   - Test payment operations:
     * Search by reference
     * Validate OTC cheque
     * Enter amount and confirm transaction

🌐 API ENDPOINTS (for testing):
===============================
The application integrates with these test APIs:

1. Get Valid Cheque:
   http://apibio2pay.webdev-test.com/DonneeTest/chequeValide/0000159
   - Use cheque number: 0000159 (configured as valid)
   - Ensure Activer, Signaler, Reserver are false

2. Get User Bank Accounts:
   http://apibio2pay.webdev-test.com/AllusercompteClient/3
   - Returns bank accounts for user ID 3

3. Search Cheque by Reference:
   http://apibio2pay.webdev-test.com/DonneeTest/reference/[reference]
   - Search for cheques by reference number

📋 FEATURES IMPLEMENTED:
========================

✅ Complete Authentication System
✅ Profile-based Access Control
✅ Responsive Modern UI Design
✅ Checkbook Management (View, Update, Associate Profiles)
✅ Check Emission with 4 Operation Types
✅ OTC Transaction Processing (Withdrawal & Payment)
✅ API Integration with Error Handling
✅ Navigation Between All Pages
✅ Data Validation and User Feedback
✅ Sample Data for Testing

🎯 USER FLOWS:
==============

1. COMPLETE WITHDRAWAL FLOW:
   Login → Main Page → Émission → Retrait OTC → Validate Cheque → Load Accounts → Enter Amount → Confirm

2. COMPLETE PAYMENT FLOW:
   Login → Main Page → Émission → Paiement OTC → Search by Reference OR Validate Cheque → Enter Amount → Confirm

3. CHECKBOOK MANAGEMENT FLOW:
   Login → Main Page → Gestion → View Checkbooks → Select → Update Configuration → Associate Profiles

🔧 TROUBLESHOOTING:
==================
If you can't open the project:
1. Make sure all files are in the same folder structure
2. Try opening ChequeSecure.wdc directly in WebDev
3. Check that your WebDev version supports the file format
4. Verify all page files exist in their respective folders
5. If still having issues, open each .wdc file individually

If API calls fail:
1. Check internet connection
2. Verify API endpoints are accessible
3. Check that test data (cheque 0000159) is available

💡 DEVELOPMENT NOTES:
====================
- All UI uses modern CSS styling with responsive design
- Data structures are defined in the main project file
- API functions include error handling and JSON parsing
- Sample data is provided for offline testing
- All text and messages are in French as requested
- Code comments and variable names are in English for development

🎉 READY TO USE:
===============
The application is fully functional and ready to run in WebDev.
All specified features from the French documentation have been implemented.
Use the quick access buttons on the login page for immediate testing!