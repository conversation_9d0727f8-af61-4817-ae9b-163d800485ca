CHÈQUESECURE - W<PERSON><PERSON>DEV PROJECT
================================

HOW TO OPEN IN WEBDEV:
======================

Method 1 (Recommended):
1. Open WebDev IDE
2. Go to File > Open Project
3. Navigate to this folder: "Cheque Secure test"
4. Select "ChequeSecure.wdcproj" file
5. Click "Open"

Method 2 (Alternative):
1. Open WebDev IDE
2. Go to File > Open
3. Navigate to this folder: "Cheque Secure test"
4. Select "ChequeSecure.wdc" file
5. Click "Open"

PROJECT STRUCTURE:
==================
- ChequeSecure.wdcproj (Main project file)
- ChequeSecure.wdc (Project configuration)
- profilsOTC/page_chequiers.wdc (Vos Chéquiers page)
- page_emission/page_chequeEmission.wdc (Émission de Chèques page)
- emission/PAGE_transactionchequeRetraitOTC.wdc (Retrait/Paiement OTC page)

TESTING:
========
- Use profile ID 1 or 3 for testing
- Test emails: <EMAIL>, <EMAIL>
- All API endpoints are configured for testing

TROUBLESHOOTING:
================
If you can't open the project:
1. Make sure all files are in the same folder
2. Try opening ChequeSecure.wdc directly
3. Check that WebDev version supports .wdcproj files
4. If still having issues, open each .wdc file individually

CONTACT:
========
For support or questions about this project. 