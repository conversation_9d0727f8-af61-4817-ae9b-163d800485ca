[PAGE]
Name=PAGE_transactionchequeRetraitOTC
Title=Retrait/Paiement par Chèque OTC
Description=Page pour retrait ou paiement par chèque OTC

[VARIABLES]
// Local variables for this page
currentOperation is string = "RETRAIT"  // RETRAIT or PAIEMENT
selectedCheque is ChequeInfo
userBankAccounts is array of CompteClient
selectedAccountIndex is int = -1
transactionAmount is currency = 0
bChequeValidated is boolean = False

[CONTROLS]
// Main container with modern styling
MainContainer is Container {
    Width: 100%
    Height: 100vh
    BackgroundColor: #f8f9fa
    FontFamily: "Segoe UI, Tahoma, Geneva, Verdana, sans-serif"
}

// Header section with navigation
HeaderSection is Container {
    Width: 100%
    Height: 80px
    BackgroundColor: #007bff
    Color: white
    Display: flex
    AlignItems: center
    JustifyContent: space-between
    Padding: 0 20px
    BoxShadow: 0 2px 4px rgba(0,0,0,0.1)
}

HeaderTitle is Static {
    Text: "ChèqueSecure - Transaction OTC"
    FontSize: 24px
    FontWeight: bold
    Color: white
}

// Navigation menu
NavMenu is Container {
    Display: flex
    Gap: 20px
}

NavLinkGestion is Button {
    Text: "Gestion"
    BackgroundColor: transparent
    Color: white
    Border: 1px solid white
    Padding: 8px 16px
    BorderRadius: 4px
    OnClick: NavToGestion
}

NavLinkEmission is Button {
    Text: "Émission"
    BackgroundColor: transparent
    Color: white
    Border: 1px solid white
    Padding: 8px 16px
    BorderRadius: 4px
    OnClick: NavToEmission
}

// Content section
ContentSection is Container {
    Width: 100%
    Height: calc(100vh - 80px)
    Padding: 20px
    OverflowY: auto
}

// Operation type section
OperationTypeSection is Container {
    Width: 100%
    MaxWidth: 800px
    Margin: 0 auto 20px auto
    BackgroundColor: white
    Border: 1px solid #dee2e6
    BorderRadius: 8px
    Padding: 20px
    BoxShadow: 0 2px 4px rgba(0,0,0,0.05)
}

OperationTitle is Static {
    Text: "Type d'opération: Retrait par chèque OTC"
    FontSize: 18px
    FontWeight: bold
    Color: #007bff
    MarginBottom: 10px
}

OperationDescription is Static {
    Text: "Retrait dans un compte bancaire via le chéquier OTC de l'institution"
    FontSize: 14px
    Color: #6c757d
}

// Cheque validation section
ChequeSection is Container {
    Width: 100%
    MaxWidth: 800px
    Margin: 0 auto 20px auto
    BackgroundColor: white
    Border: 1px solid #dee2e6
    BorderRadius: 8px
    Padding: 20px
    BoxShadow: 0 2px 4px rgba(0,0,0,0.05)
}

ChequeSectionTitle is Static {
    Text: "Validation du chèque"
    FontSize: 16px
    FontWeight: bold
    MarginBottom: 15px
    Color: #333
}

// Cheque number input
ChequeNumberContainer is Container {
    Display: flex
    AlignItems: center
    Gap: 15px
    MarginBottom: 20px
}

ChequeNumberLabel is Static {
    Text: "Numéro de chèque:"
    FontWeight: bold
    MinWidth: 150px
}

EDT_CHEQUE_NUMBER is Edit {
    Placeholder: "Ex: 0000159"
    Width: 200px
    Height: 35px
    Border: 1px solid #ced4da
    BorderRadius: 4px
    Padding: 8px 12px
}

BTN_GET_CHEQUE is Button {
    Text: "Valider le chèque"
    Width: 150px
    Height: 35px
    BackgroundColor: #007bff
    Color: white
    Border: none
    BorderRadius: 4px
    OnClick: BTN_GET_CHEQUE_Click
    Cursor: pointer
}

// Reference search (for payment operations)
ReferenceContainer is Container {
    Display: flex
    AlignItems: center
    Gap: 15px
    MarginBottom: 20px
    Visible: False
}

ReferenceLabel is Static {
    Text: "Référence:"
    FontWeight: bold
    MinWidth: 150px
}

EDT_REF is Edit {
    Placeholder: "Référence du reçu/facture"
    Width: 200px
    Height: 35px
    Border: 1px solid #ced4da
    BorderRadius: 4px
    Padding: 8px 12px
}

BTN_SEARCH_REF is Button {
    Text: "Rechercher"
    Width: 150px
    Height: 35px
    BackgroundColor: #17a2b8
    Color: white
    Border: none
    BorderRadius: 4px
    OnClick: BTN_SEARCH_REF_Click
    Cursor: pointer
}

// Cheque details display
ChequeDetailsSection is Container {
    Width: 100%
    BackgroundColor: #f8f9fa
    Border: 1px solid #e9ecef
    BorderRadius: 6px
    Padding: 15px
    MarginBottom: 20px
    Visible: False
}

ChequeDetailsTitle is Static {
    Text: "Détails du chèque"
    FontSize: 14px
    FontWeight: bold
    MarginBottom: 10px
    Color: #495057
}

ChequeDetailsText is Static {
    Text: ""
    FontSize: 12px
    Color: #6c757d
    WhiteSpace: pre-line
}

// Bank account selection section (for withdrawal operations)
BankAccountSection is Container {
    Width: 100%
    MaxWidth: 800px
    Margin: 0 auto 20px auto
    BackgroundColor: white
    Border: 1px solid #dee2e6
    BorderRadius: 8px
    Padding: 20px
    BoxShadow: 0 2px 4px rgba(0,0,0,0.05)
    Visible: False
}

BankAccountTitle is Static {
    Text: "Sélection du compte bancaire"
    FontSize: 16px
    FontWeight: bold
    MarginBottom: 15px
    Color: #333
}

BankAccountContainer is Container {
    Display: flex
    AlignItems: center
    Gap: 15px
    MarginBottom: 20px
}

BankAccountLabel is Static {
    Text: "Compte bancaire:"
    FontWeight: bold
    MinWidth: 150px
}

COMBO_BANK_ACCOUNT is Combo {
    Width: 300px
    Height: 35px
    Border: 1px solid #ced4da
    BorderRadius: 4px
    Padding: 8px 12px
    OnChange: COMBO_BANK_ACCOUNT_Change
}

BTN_LOAD_ACCOUNTS is Button {
    Text: "Charger les comptes"
    Width: 150px
    Height: 35px
    BackgroundColor: #28a745
    Color: white
    Border: none
    BorderRadius: 4px
    OnClick: BTN_LOAD_ACCOUNTS_Click
    Cursor: pointer
}

// Transaction amount section
AmountSection is Container {
    Width: 100%
    MaxWidth: 800px
    Margin: 0 auto 20px auto
    BackgroundColor: white
    Border: 1px solid #dee2e6
    BorderRadius: 8px
    Padding: 20px
    BoxShadow: 0 2px 4px rgba(0,0,0,0.05)
    Visible: False
}

AmountTitle is Static {
    Text: "Montant de la transaction"
    FontSize: 16px
    FontWeight: bold
    MarginBottom: 15px
    Color: #333
}

AmountContainer is Container {
    Display: flex
    AlignItems: center
    Gap: 15px
    MarginBottom: 20px
}

AmountLabel is Static {
    Text: "Montant:"
    FontWeight: bold
    MinWidth: 150px
}

EDT_AMOUNT is Edit {
    Placeholder: "0.00"
    Width: 200px
    Height: 35px
    Border: 1px solid #ced4da
    BorderRadius: 4px
    Padding: 8px 12px
    Type: Currency
}

CurrencyLabel is Static {
    Text: "FCFA"
    FontWeight: bold
    Color: #6c757d
}

// Transaction confirmation section
ConfirmationSection is Container {
    Width: 100%
    MaxWidth: 800px
    Margin: 0 auto 20px auto
    BackgroundColor: white
    Border: 1px solid #dee2e6
    BorderRadius: 8px
    Padding: 20px
    BoxShadow: 0 2px 4px rgba(0,0,0,0.05)
    Visible: False
}

ConfirmationTitle is Static {
    Text: "Confirmation de la transaction"
    FontSize: 16px
    FontWeight: bold
    MarginBottom: 15px
    Color: #333
}

TransactionSummary is Static {
    Text: ""
    FontSize: 14px
    Color: #495057
    MarginBottom: 20px
    WhiteSpace: pre-line
}

ButtonContainer is Container {
    Display: flex
    JustifyContent: center
    Gap: 20px
}

BTN_CONFIRM is Button {
    Text: "Confirmer la transaction"
    Width: 200px
    Height: 45px
    BackgroundColor: #28a745
    Color: white
    Border: none
    BorderRadius: 6px
    FontSize: 16px
    FontWeight: bold
    OnClick: BTN_CONFIRM_Click
    Cursor: pointer
    State: Grayed
}

BTN_CANCEL is Button {
    Text: "Annuler"
    Width: 120px
    Height: 45px
    BackgroundColor: #6c757d
    Color: white
    Border: none
    BorderRadius: 6px
    FontSize: 16px
    OnClick: BTN_CANCEL_Click
    Cursor: pointer
}

// Status and message section
StatusSection is Container {
    Width: 100%
    MaxWidth: 800px
    Margin: 0 auto
    TextAlign: center
}

STC_RESULT is Static {
    Text: ""
    FontSize: 14px
    Color: #28a745
    FontWeight: bold
    MarginBottom: 10px
}

STC_ERROR is Static {
    Text: ""
    FontSize: 14px
    Color: #dc3545
    FontWeight: bold
}

[FUNCTIONS]
// Page initialization
PROCEDURE InitializePage()
    // Determine operation type from global variable or default to RETRAIT
    IF gOperationType <> "" THEN
        currentOperation = gOperationType
    ELSE
        currentOperation = "RETRAIT"
    END

    // Update UI based on operation type
    UpdateUIForOperation()

    // Show welcome message
    STC_RESULT = "Veuillez valider un chèque pour commencer la transaction"
END

// Update UI based on operation type
PROCEDURE UpdateUIForOperation()
    IF currentOperation = "RETRAIT" THEN
        OperationTitle = "Type d'opération: Retrait par chèque OTC"
        OperationDescription = "Retrait dans un compte bancaire via le chéquier OTC de l'institution"
        HeaderTitle = "ChèqueSecure - Retrait OTC"
        ReferenceContainer..Visible = False
        BankAccountSection..Visible = False  // Will be shown after cheque validation
    ELSE IF currentOperation = "PAIEMENT" THEN
        OperationTitle = "Type d'opération: Paiement par chèque OTC"
        OperationDescription = "Paiement d'un reçu ou facture via le chéquier OTC du magasin ou de la boutique"
        HeaderTitle = "ChèqueSecure - Paiement OTC"
        ReferenceContainer..Visible = True
        BankAccountSection..Visible = False  // Not needed for payment
    END
END

// Navigation functions
PROCEDURE NavToGestion()
    PageDisplay("profilsOTC/page_chequiers")
END

PROCEDURE NavToEmission()
    PageDisplay("page_emission/page_chequeEmission")
END

// Validate cheque using API
PROCEDURE BTN_GET_CHEQUE_Click()
    chequeNumber is string = EDT_CHEQUE_NUMBER
    STC_ERROR = ""
    STC_RESULT = ""

    IF Length(chequeNumber) = 0 THEN
        STC_ERROR = "Veuillez saisir un numéro de chèque"
        RETURN
    END

    // Call API to get cheque information
    STC_RESULT = "Validation du chèque en cours..."
    chequeData is string = GetValidCheque(chequeNumber)

    IF Length(chequeData) = 0 THEN
        STC_ERROR = "Erreur lors de la récupération des données du chèque"
        RETURN
    END

    // Parse the JSON response (simplified)
    selectedCheque = ParseChequeJSON(chequeData)

    // Validate cheque for transaction
    IF NOT ValidateChequeForTransaction(selectedCheque) THEN
        STC_ERROR = "Ce chèque ne peut pas être utilisé (Activé, Signalé ou Réservé)"
        ChequeDetailsText = "Chèque: " + selectedCheque.Numero + "\nStatut: Non valide pour transaction"
        ChequeDetailsSection..Visible = True
        RETURN
    END

    // Check if it's an OTC cheque for payment operations
    IF currentOperation = "PAIEMENT" AND selectedCheque.Type <> "OTC" THEN
        STC_ERROR = "Seuls les chèques OTC sont autorisés pour les paiements"
        RETURN
    END

    // Display cheque details
    ChequeDetailsText = "Chèque: " + selectedCheque.Numero + "\nType: " + selectedCheque.Type + "\nStatut: Valide pour transaction"
    ChequeDetailsSection..Visible = True
    bChequeValidated = True
    STC_RESULT = "Chèque validé avec succès"

    // Show next step based on operation type
    IF currentOperation = "RETRAIT" THEN
        BankAccountSection..Visible = True
        STC_RESULT = STC_RESULT + " - Veuillez charger vos comptes bancaires"
    ELSE IF currentOperation = "PAIEMENT" THEN
        AmountSection..Visible = True
        STC_RESULT = STC_RESULT + " - Veuillez saisir le montant"
    END
END

// Search by reference (for payment operations)
PROCEDURE BTN_SEARCH_REF_Click()
    ref is string = EDT_REF
    STC_ERROR = ""
    STC_RESULT = ""

    IF Length(ref) = 0 THEN
        STC_ERROR = "Veuillez saisir une référence"
        RETURN
    END

    // Call API to search cheque by reference
    STC_RESULT = "Recherche en cours..."
    chequeData is string = SearchChequeByReference(ref)

    IF Length(chequeData) = 0 THEN
        STC_ERROR = "Aucun chèque trouvé pour cette référence"
        RETURN
    END

    // Parse and validate the cheque
    selectedCheque = ParseChequeJSON(chequeData)

    IF NOT ValidateChequeForTransaction(selectedCheque) THEN
        STC_ERROR = "Le chèque trouvé ne peut pas être utilisé"
        RETURN
    END

    IF selectedCheque.Type <> "OTC" THEN
        STC_ERROR = "Seuls les chèques OTC sont autorisés pour les paiements"
        RETURN
    END

    // Update cheque number field and display details
    EDT_CHEQUE_NUMBER = selectedCheque.Numero
    ChequeDetailsText = "Chèque: " + selectedCheque.Numero + "\nRéférence: " + ref + "\nType: " + selectedCheque.Type + "\nStatut: Valide"
    ChequeDetailsSection..Visible = True
    bChequeValidated = True
    AmountSection..Visible = True
    STC_RESULT = "Chèque trouvé et validé - Veuillez saisir le montant"
END

// Load user bank accounts
PROCEDURE BTN_LOAD_ACCOUNTS_Click()
    STC_ERROR = ""
    STC_RESULT = "Chargement des comptes bancaires..."

    // Call API to get user bank accounts
    accountsData is string = GetUserBankAccounts(gIDProfil)

    IF Length(accountsData) = 0 THEN
        STC_ERROR = "Erreur lors du chargement des comptes bancaires"
        RETURN
    END

    // Parse accounts data (simplified - in real implementation, parse JSON)
    userBankAccounts = []

    // Sample account data for testing
    account1 is CompteClient
    account1.IDCompte = 1
    account1.NumeroCompte = "**********"
    account1.TypeCompte = "Courant"
    account1.Solde = 150000
    account1.Devise = "FCFA"
    account1.Etat = "Actif"
    userBankAccounts.Add(account1)

    account2 is CompteClient
    account2.IDCompte = 2
    account2.NumeroCompte = "**********"
    account2.TypeCompte = "Épargne"
    account2.Solde = 75000
    account2.Devise = "FCFA"
    account2.Etat = "Actif"
    userBankAccounts.Add(account2)

    // Populate combo box
    COMBO_BANK_ACCOUNT..Clear()
    FOR i = 1 TO userBankAccounts..Count
        accountText is string = userBankAccounts[i].NumeroCompte + " - " + userBankAccounts[i].TypeCompte + " (" + userBankAccounts[i].Solde + " " + userBankAccounts[i].Devise + ")"
        COMBO_BANK_ACCOUNT..Add(accountText)
    END

    STC_RESULT = "Comptes bancaires chargés - Veuillez sélectionner un compte"
    AmountSection..Visible = True
END

// Handle bank account selection
PROCEDURE COMBO_BANK_ACCOUNT_Change()
    selectedAccountIndex = COMBO_BANK_ACCOUNT..SelectedIndex
    IF selectedAccountIndex > 0 THEN
        STC_RESULT = "Compte sélectionné - Veuillez saisir le montant"
    END
END

// Prepare transaction confirmation
PROCEDURE PrepareTransactionConfirmation()
    transactionAmount = EDT_AMOUNT

    IF transactionAmount <= 0 THEN
        STC_ERROR = "Veuillez saisir un montant valide"
        RETURN
    END

    // Build transaction summary
    summary is string = "Résumé de la transaction:\n\n"
    summary = summary + "Type: " + currentOperation + "\n"
    summary = summary + "Chèque: " + selectedCheque.Numero + "\n"
    summary = summary + "Montant: " + transactionAmount + " FCFA\n"

    IF currentOperation = "RETRAIT" THEN
        IF selectedAccountIndex <= 0 THEN
            STC_ERROR = "Veuillez sélectionner un compte bancaire"
            RETURN
        END
        summary = summary + "Compte: " + userBankAccounts[selectedAccountIndex].NumeroCompte + "\n"
        summary = summary + "Type de compte: " + userBankAccounts[selectedAccountIndex].TypeCompte
    END

    TransactionSummary = summary
    ConfirmationSection..Visible = True
    BTN_CONFIRM..State = Active
    STC_RESULT = "Vérifiez les détails et confirmez la transaction"
END

// Confirm transaction
PROCEDURE BTN_CONFIRM_Click()
    STC_ERROR = ""
    STC_RESULT = "Traitement de la transaction en cours..."

    // In a real application, this would call an API to process the transaction
    // For demo purposes, we'll simulate the transaction

    // Simulate processing delay
    Timer(2000)

    // Generate transaction ID
    transactionID is string = "TXN" + DateToString(DateSys(), "YYYYMMDD") + TimeToString(TimeSys(), "HHMMSS")

    STC_RESULT = "Transaction réussie!\n"
    STC_RESULT = STC_RESULT + "ID Transaction: " + transactionID + "\n"
    STC_RESULT = STC_RESULT + "Montant: " + transactionAmount + " FCFA\n"
    STC_RESULT = STC_RESULT + "Date: " + DateToString(DateSys(), "DD/MM/YYYY") + " " + TimeToString(TimeSys(), "HH:MM:SS")

    // Disable confirmation button
    BTN_CONFIRM..State = Grayed
    BTN_CONFIRM..Text = "Transaction terminée"

    Info("Transaction réussie! ID: " + transactionID)
END

// Cancel transaction
PROCEDURE BTN_CANCEL_Click()
    result is int = YesNo("Êtes-vous sûr de vouloir annuler cette transaction?")
    IF result = Yes THEN
        // Reset form
        EDT_CHEQUE_NUMBER = ""
        EDT_REF = ""
        EDT_AMOUNT = 0
        COMBO_BANK_ACCOUNT..SelectedIndex = 0

        // Hide sections
        ChequeDetailsSection..Visible = False
        BankAccountSection..Visible = False
        AmountSection..Visible = False
        ConfirmationSection..Visible = False

        // Reset variables
        bChequeValidated = False
        selectedAccountIndex = -1
        transactionAmount = 0

        STC_RESULT = "Transaction annulée"
        STC_ERROR = ""
    END
END

[EVENTS]
// Page load event
OnPageLoad: InitializePage

// Amount field change event to trigger confirmation preparation
OnChange_EDT_AMOUNT: PrepareTransactionConfirmation