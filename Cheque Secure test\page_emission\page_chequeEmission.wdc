[PAGE]
Name=page_chequeEmission
Title=Émission de Chèques
Description=Page d'émission de chèques avec quatre types d'opérations

[VARIABLES]
// Local variables for this page
bHasClassicCheckbook is boolean = False
bHasPrepaidCheckbook is boolean = False
userCheckbooks is array of Chequier

[CONTROLS]
// Main container with modern styling
MainContainer is Container {
    Width: 100%
    Height: 100vh
    BackgroundColor: #f8f9fa
    FontFamily: "Segoe UI, Tahoma, Geneva, Verdana, sans-serif"
}

// Header section with navigation
HeaderSection is Container {
    Width: 100%
    Height: 80px
    BackgroundColor: #007bff
    Color: white
    Display: flex
    AlignItems: center
    JustifyContent: space-between
    Padding: 0 20px
    BoxShadow: 0 2px 4px rgba(0,0,0,0.1)
}

HeaderTitle is Static {
    Text: "ChèqueSecure - Émission de Chèques"
    FontSize: 24px
    FontWeight: bold
    Color: white
}

// Navigation menu
NavMenu is Container {
    Display: flex
    Gap: 20px
}

NavLinkGestion is Button {
    Text: "Gestion"
    BackgroundColor: transparent
    Color: white
    Border: 1px solid white
    Padding: 8px 16px
    BorderRadius: 4px
    OnClick: NavToGestion
}

NavLinkEmission is Button {
    Text: "Émission"
    BackgroundColor: rgba(255,255,255,0.2)
    Color: white
    Border: 1px solid white
    Padding: 8px 16px
    BorderRadius: 4px
    FontWeight: bold
}

// Content section
ContentSection is Container {
    Width: 100%
    Height: calc(100vh - 80px)
    Padding: 40px 20px
    OverflowY: auto
}

// User info section
UserInfoSection is Container {
    Width: 100%
    MaxWidth: 800px
    Margin: 0 auto 30px auto
    BackgroundColor: white
    Border: 1px solid #dee2e6
    BorderRadius: 8px
    Padding: 20px
    BoxShadow: 0 2px 4px rgba(0,0,0,0.05)
}

UserInfoText is Static {
    Text: "Utilisateur connecté - Profil ID: " + gIDProfil
    FontSize: 14px
    Color: #6c757d
    MarginBottom: 10px
}

AccessInfoText is Static {
    Text: "Accès autorisé aux chéquiers classiques et prépayés"
    FontSize: 14px
    Color: #28a745
    FontWeight: bold
}

// Main operations section
OperationsSection is Container {
    Width: 100%
    MaxWidth: 800px
    Margin: 0 auto
    BackgroundColor: white
    Border: 1px solid #dee2e6
    BorderRadius: 8px
    Padding: 30px
    BoxShadow: 0 2px 4px rgba(0,0,0,0.05)
}

SectionTitle is Static {
    Text: "Choisissez le type d'opération"
    FontSize: 20px
    FontWeight: bold
    MarginBottom: 30px
    TextAlign: center
    Color: #333
}

// Operations grid
OperationsGrid is Container {
    Display: grid
    GridTemplateColumns: 1fr 1fr
    Gap: 20px
    MarginBottom: 30px
}

// OTC Operations Card
OTCCard is Container {
    BackgroundColor: #e7f3ff
    Border: 2px solid #007bff
    BorderRadius: 8px
    Padding: 20px
    TextAlign: center
}

OTCTitle is Static {
    Text: "Opérations OTC"
    FontSize: 16px
    FontWeight: bold
    Color: #007bff
    MarginBottom: 15px
}

BTN_RETRAIT is Button {
    Text: "Retrait par chèque OTC"
    Width: 100%
    Height: 50px
    BackgroundColor: #007bff
    Color: white
    Border: none
    BorderRadius: 6px
    FontSize: 14px
    FontWeight: bold
    MarginBottom: 10px
    OnClick: BTN_RETRAIT_Click
    Cursor: pointer
}

BTN_PAIEMENT is Button {
    Text: "Paiement par chèque OTC"
    Width: 100%
    Height: 50px
    BackgroundColor: #17a2b8
    Color: white
    Border: none
    BorderRadius: 6px
    FontSize: 14px
    FontWeight: bold
    OnClick: BTN_PAIEMENT_Click
    Cursor: pointer
}

// Standard Operations Card
StandardCard is Container {
    BackgroundColor: #f8f9fa
    Border: 2px solid #6c757d
    BorderRadius: 8px
    Padding: 20px
    TextAlign: center
}

StandardTitle is Static {
    Text: "Émissions Standard"
    FontSize: 16px
    FontWeight: bold
    Color: #6c757d
    MarginBottom: 15px
}

BTN_CLASSIQUE is Button {
    Text: "Émission de chèque classique"
    Width: 100%
    Height: 50px
    BackgroundColor: #28a745
    Color: white
    Border: none
    BorderRadius: 6px
    FontSize: 14px
    FontWeight: bold
    MarginBottom: 10px
    OnClick: BTN_CLASSIQUE_Click
    Cursor: pointer
}

BTN_PREPAYE is Button {
    Text: "Émission de chèque prépayé"
    Width: 100%
    Height: 50px
    BackgroundColor: #ffc107
    Color: #212529
    Border: none
    BorderRadius: 6px
    FontSize: 14px
    FontWeight: bold
    OnClick: BTN_PREPAYE_Click
    Cursor: pointer
}

// Status and message section
StatusSection is Container {
    Width: 100%
    MarginTop: 30px
    TextAlign: center
}

STC_INFO is Static {
    Text: ""
    FontSize: 14px
    Color: #28a745
    FontWeight: bold
    MarginBottom: 10px
}

STC_ERROR is Static {
    Text: ""
    FontSize: 14px
    Color: #dc3545
    FontWeight: bold
}

[FUNCTIONS]
// Page initialization
PROCEDURE InitializePage()
    // Check if user is connected with valid profile
    IF gIDProfil <> 1 AND gIDProfil <> 3 THEN
        STC_ERROR = "Accès non autorisé. Profil requis: 1 ou 3"
        AccessInfoText..Visible = False
        // Disable all buttons
        BTN_RETRAIT..State = Grayed
        BTN_PAIEMENT..State = Grayed
        BTN_CLASSIQUE..State = Grayed
        BTN_PREPAYE..State = Grayed
        RETURN
    END

    // Update user info display
    UserInfoText = "Utilisateur connecté - Profil ID: " + gIDProfil + " | Email: " + gUserEmail

    // Check checkbook configurations
    CheckChequierConfigurations()

    // Show welcome message
    STC_INFO = "Bienvenue dans le module d'émission de chèques"
END

// Navigation functions
PROCEDURE NavToGestion()
    PageDisplay("profilsOTC/page_chequiers")
END

// Check available checkbook types for the user
PROCEDURE CheckChequierConfigurations()
    // In a real application, this would call an API to check user's checkbooks
    // For demo purposes, we'll assume user has access based on profile

    IF gIDProfil = 1 OR gIDProfil = 3 THEN
        bHasClassicCheckbook = True
        bHasPrepaidCheckbook = True
        AccessInfoText = "Accès autorisé aux chéquiers classiques et prépayés"
        AccessInfoText..Color = "#28a745"
    ELSE
        bHasClassicCheckbook = False
        bHasPrepaidCheckbook = False
        AccessInfoText = "Accès limité - Contactez votre administrateur"
        AccessInfoText..Color = "#dc3545"
        BTN_CLASSIQUE..State = Grayed
        BTN_PREPAYE..State = Grayed
    END
END

// Button click handlers
PROCEDURE BTN_RETRAIT_Click()
    STC_ERROR = ""
    STC_INFO = "Redirection vers la page de retrait OTC..."

    // Add operation type parameter for the transaction page
    gOperationType is string = "RETRAIT"
    PageDisplay("emission/PAGE_transactionchequeRetraitOTC")
END

PROCEDURE BTN_PAIEMENT_Click()
    STC_ERROR = ""
    STC_INFO = "Redirection vers la page de paiement OTC..."

    // Add operation type parameter for the transaction page
    gOperationType is string = "PAIEMENT"
    PageDisplay("emission/PAGE_transactionchequeRetraitOTC")
END

PROCEDURE BTN_CLASSIQUE_Click()
    STC_ERROR = ""

    IF NOT bHasClassicCheckbook THEN
        STC_ERROR = "Vous n'avez pas accès aux chéquiers classiques"
        RETURN
    END

    STC_INFO = "Émission de chèque classique sélectionnée"
    // In a real application, this would redirect to a classic check emission page
    // For now, we'll show a message
    Info("Fonctionnalité d'émission de chèque classique - À implémenter")
END

PROCEDURE BTN_PREPAYE_Click()
    STC_ERROR = ""

    IF NOT bHasPrepaidCheckbook THEN
        STC_ERROR = "Vous n'avez pas accès aux chéquiers prépayés"
        RETURN
    END

    STC_INFO = "Émission de chèque prépayé sélectionnée"
    // In a real application, this would redirect to a prepaid check emission page
    // For now, we'll show a message
    Info("Fonctionnalité d'émission de chèque prépayé - À implémenter")
END

[EVENTS]
// Page load event
OnPageLoad: InitializePage