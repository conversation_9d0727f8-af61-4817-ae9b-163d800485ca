[PAGE]
Name=page_login
Title=ChèqueSecure - Connexion
Description=Page de connexion pour accéder au système ChèqueSecure

[VARIABLES]
// Local variables for this page
loginAttempts is int = 0
maxLoginAttempts is int = 3

[CONTROLS]
// Main container with modern styling
MainContainer is Container {
    Width: 100%
    Height: 100vh
    BackgroundColor: #f8f9fa
    FontFamily: "Segoe UI, Tahoma, Geneva, Verdana, sans-serif"
    Display: flex
    AlignItems: center
    JustifyContent: center
}

// Login card
LoginCard is Container {
    Width: 400px
    BackgroundColor: white
    Border: 1px solid #dee2e6
    BorderRadius: 12px
    Padding: 40px
    BoxShadow: 0 8px 16px rgba(0,0,0,0.1)
    TextAlign: center
}

// Header section
HeaderSection is Container {
    MarginBottom: 30px
}

AppLogo is Static {
    Text: "🔐"
    FontSize: 48px
    MarginBottom: 15px
}

AppTitle is Static {
    Text: "ChèqueSecure"
    FontSize: 28px
    FontWeight: bold
    Color: #007bff
    MarginBottom: 5px
}

AppSubtitle is Static {
    Text: "Connexion Sécurisée"
    FontSize: 16px
    Color: #6c757d
    MarginBottom: 30px
}

// Login form
LoginForm is Container {
    TextAlign: left
}

// Profile ID selection
ProfileLabel is Static {
    Text: "Profil d'accès:"
    FontWeight: bold
    MarginBottom: 8px
    Color: #333
}

COMBO_PROFILE is Combo {
    Width: 100%
    Height: 40px
    Border: 1px solid #ced4da
    BorderRadius: 6px
    Padding: 8px 12px
    MarginBottom: 20px
    Content: ["Sélectionner un profil", "Profil 1 - Administrateur", "Profil 3 - Utilisateur Standard"]
}

// Email field
EmailLabel is Static {
    Text: "Adresse e-mail:"
    FontWeight: bold
    MarginBottom: 8px
    Color: #333
}

EDT_EMAIL is Edit {
    Placeholder: "Votre adresse e-mail"
    Width: 100%
    Height: 40px
    Border: 1px solid #ced4da
    BorderRadius: 6px
    Padding: 8px 12px
    MarginBottom: 20px
}

// Password field
PasswordLabel is Static {
    Text: "Mot de passe:"
    FontWeight: bold
    MarginBottom: 8px
    Color: #333
}

EDT_PASSWORD is Edit {
    Placeholder: "Votre mot de passe"
    Width: 100%
    Height: 40px
    Border: 1px solid #ced4da
    BorderRadius: 6px
    Padding: 8px 12px
    MarginBottom: 25px
    Type: Password
}

// Login button
BTN_LOGIN is Button {
    Text: "Se connecter"
    Width: 100%
    Height: 45px
    BackgroundColor: #007bff
    Color: white
    Border: none
    BorderRadius: 6px
    FontSize: 16px
    FontWeight: bold
    OnClick: BTN_LOGIN_Click
    Cursor: pointer
    MarginBottom: 20px
}

// Quick access section
QuickAccessSection is Container {
    MarginTop: 20px
    Padding: 15px
    BackgroundColor: #f8f9fa
    BorderRadius: 6px
    Border: 1px solid #e9ecef
}

QuickAccessTitle is Static {
    Text: "Accès rapide pour les tests:"
    FontSize: 14px
    FontWeight: bold
    Color: #495057
    MarginBottom: 10px
}

BTN_QUICK_PROFILE1 is Button {
    Text: "Profil 1 (Admin)"
    Width: 48%
    Height: 35px
    BackgroundColor: #28a745
    Color: white
    Border: none
    BorderRadius: 4px
    FontSize: 12px
    OnClick: BTN_QUICK_PROFILE1_Click
    Cursor: pointer
    MarginRight: 4%
}

BTN_QUICK_PROFILE3 is Button {
    Text: "Profil 3 (User)"
    Width: 48%
    Height: 35px
    BackgroundColor: #17a2b8
    Color: white
    Border: none
    BorderRadius: 4px
    FontSize: 12px
    OnClick: BTN_QUICK_PROFILE3_Click
    Cursor: pointer
}

// Status messages
StatusSection is Container {
    MarginTop: 20px
    TextAlign: center
}

STC_INFO is Static {
    Text: ""
    FontSize: 14px
    Color: #28a745
    FontWeight: bold
    MarginBottom: 10px
}

STC_ERROR is Static {
    Text: ""
    FontSize: 14px
    Color: #dc3545
    FontWeight: bold
}

// Footer
FooterSection is Container {
    MarginTop: 30px
    TextAlign: center
    Padding: 15px
    BorderTop: 1px solid #dee2e6
}

FooterText is Static {
    Text: "ChèqueSecure v1.0 - Accès sécurisé requis"
    FontSize: 12px
    Color: #6c757d
}

[FUNCTIONS]
// Page initialization
PROCEDURE InitializePage()
    // Reset login state
    gIsConnected = False
    gIDProfil = 0
    gUserEmail = ""
    gUserName = ""
    
    // Show welcome message
    STC_INFO = "Veuillez vous connecter pour accéder au système"
END

// Handle login
PROCEDURE BTN_LOGIN_Click()
    STC_ERROR = ""
    STC_INFO = ""
    
    // Get form values
    selectedProfile is int = COMBO_PROFILE..SelectedIndex
    email is string = EDT_EMAIL
    password is string = EDT_PASSWORD
    
    // Validate inputs
    IF selectedProfile <= 0 THEN
        STC_ERROR = "Veuillez sélectionner un profil"
        RETURN
    END
    
    IF Length(email) = 0 THEN
        STC_ERROR = "Veuillez saisir votre adresse e-mail"
        RETURN
    END
    
    IF Length(password) = 0 THEN
        STC_ERROR = "Veuillez saisir votre mot de passe"
        RETURN
    END
    
    // Validate email format
    IF Position(email, "@") = 0 OR Position(email, ".") = 0 THEN
        STC_ERROR = "Format d'e-mail invalide"
        RETURN
    END
    
    // Authenticate user (simplified for demo)
    IF AuthenticateUser(selectedProfile, email, password) THEN
        STC_INFO = "Connexion réussie! Redirection en cours..."
        Timer(1000)  // Brief delay to show success message
        PageDisplay("page_main")
    ELSE
        loginAttempts = loginAttempts + 1
        STC_ERROR = "Identifiants incorrects (Tentative " + loginAttempts + "/" + maxLoginAttempts + ")"
        
        IF loginAttempts >= maxLoginAttempts THEN
            STC_ERROR = "Trop de tentatives de connexion. Accès bloqué."
            BTN_LOGIN..State = Grayed
        END
    END
END

// Quick access functions for testing
PROCEDURE BTN_QUICK_PROFILE1_Click()
    COMBO_PROFILE..SelectedIndex = 1
    EDT_EMAIL = "<EMAIL>"
    EDT_PASSWORD = "admin123"
    BTN_LOGIN_Click()
END

PROCEDURE BTN_QUICK_PROFILE3_Click()
    COMBO_PROFILE..SelectedIndex = 2
    EDT_EMAIL = "<EMAIL>"
    EDT_PASSWORD = "user123"
    BTN_LOGIN_Click()
END

// Authenticate user function
FUNCTION AuthenticateUser(profileIndex is int, email is string, password is string) is boolean
    // Simplified authentication for demo
    // In a real application, this would call an authentication API
    
    IF profileIndex = 1 AND email = "<EMAIL>" AND password = "admin123" THEN
        // Profile 1 - Administrator
        gIDProfil = 1
        gUserEmail = email
        gUserName = "Administrateur"
        gIsConnected = True
        RESULT True
    ELSE IF profileIndex = 2 AND email = "<EMAIL>" AND password = "user123" THEN
        // Profile 3 - Standard User
        gIDProfil = 3
        gUserEmail = email
        gUserName = "Utilisateur Standard"
        gIsConnected = True
        RESULT True
    ELSE IF email = "<EMAIL>" AND password = "test123" THEN
        // Test user from specifications
        gIDProfil = 1
        gUserEmail = email
        gUserName = "Bibouth Lep"
        gIsConnected = True
        RESULT True
    ELSE IF email = "<EMAIL>" AND password = "test123" THEN
        // Test user from specifications
        gIDProfil = 3
        gUserEmail = email
        gUserName = "Celine Bobo"
        gIsConnected = True
        RESULT True
    END
    
    RESULT False
END

[EVENTS]
// Page load event
OnPageLoad: InitializePage
