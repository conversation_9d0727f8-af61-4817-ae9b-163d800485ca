// PAGE: page_login
// TITLE: ChèqueSecure - Connexion
// DESCRIPTION: Page de connexion pour accéder au système ChèqueSecure

// ===== VARIABLES =====
loginAttempts is int = 0
maxLoginAttempts is int = 3

// ===== CONTROLS =====

// Main container
MainContainer is Control
	Type = Container
	Width = 100%
	Height = 100vh
	BackgroundColor = #f8f9fa
	FontFamily = "Segoe UI, Tahoma, Geneva, Verdana, sans-serif"
	Display = flex
	AlignItems = center
	JustifyContent = center
END

// Login card
LoginCard is Control
	Type = Container
	Width = 400px
	BackgroundColor = white
	Border = 1px solid #dee2e6
	BorderRadius = 12px
	Padding = 40px
	BoxShadow = 0 8px 16px rgba(0,0,0,0.1)
	TextAlign = center
	Parent = MainContainer
END

// App title
AppTitle is Control
	Type = Static
	Text = "ChèqueSecure"
	FontSize = 28px
	FontWeight = bold
	Color = #007bff
	MarginBottom = 5px
	Parent = LoginCard
END

// App subtitle
AppSubtitle is Control
	Type = Static
	Text = "Connexion Sécurisée"
	FontSize = 16px
	Color = #6c757d
	MarginBottom = 30px
	Parent = LoginCard
END

// Profile selection
ProfileLabel is Control
	Type = Static
	Text = "Profil d'accès:"
	FontWeight = bold
	MarginBottom = 8px
	Color = #333
	Parent = LoginCard
END

COMBO_PROFILE is Control
	Type = Combo
	Width = 100%
	Height = 40px
	Border = 1px solid #ced4da
	BorderRadius = 6px
	Padding = 8px 12px
	MarginBottom = 20px
	Parent = LoginCard
	Content = ["Sélectionner un profil", "Profil 1 - Administrateur", "Profil 3 - Utilisateur Standard"]
END

// Email field
EmailLabel is Control
	Type = Static
	Text = "Adresse e-mail:"
	FontWeight = bold
	MarginBottom = 8px
	Color = #333
	Parent = LoginCard
END

EDT_EMAIL is Control
	Type = Edit
	Placeholder = "Votre adresse e-mail"
	Width = 100%
	Height = 40px
	Border = 1px solid #ced4da
	BorderRadius = 6px
	Padding = 8px 12px
	MarginBottom = 20px
	Parent = LoginCard
END

// Password field
PasswordLabel is Control
	Type = Static
	Text = "Mot de passe:"
	FontWeight = bold
	MarginBottom = 8px
	Color = #333
	Parent = LoginCard
END

EDT_PASSWORD is Control
	Type = Edit
	Placeholder = "Votre mot de passe"
	Width = 100%
	Height = 40px
	Border = 1px solid #ced4da
	BorderRadius = 6px
	Padding = 8px 12px
	MarginBottom = 25px
	Type = Password
	Parent = LoginCard
END

// Login button
BTN_LOGIN is Control
	Type = Button
	Text = "Se connecter"
	Width = 100%
	Height = 45px
	BackgroundColor = #007bff
	Color = white
	Border = none
	BorderRadius = 6px
	FontSize = 16px
	FontWeight = bold
	Cursor = pointer
	MarginBottom = 20px
	Parent = LoginCard
END

// Quick access buttons
BTN_QUICK_PROFILE1 is Control
	Type = Button
	Text = "Profil 1 (Admin)"
	Width = 48%
	Height = 35px
	BackgroundColor = #28a745
	Color = white
	Border = none
	BorderRadius = 4px
	FontSize = 12px
	Cursor = pointer
	MarginRight = 4%
	Parent = LoginCard
END

BTN_QUICK_PROFILE3 is Control
	Type = Button
	Text = "Profil 3 (User)"
	Width = 48%
	Height = 35px
	BackgroundColor = #17a2b8
	Color = white
	Border = none
	BorderRadius = 4px
	FontSize = 12px
	Cursor = pointer
	Parent = LoginCard
END

// Status messages
STC_INFO is Control
	Type = Static
	Text = ""
	FontSize = 14px
	Color = #28a745
	FontWeight = bold
	MarginBottom = 10px
	Parent = LoginCard
END

STC_ERROR is Control
	Type = Static
	Text = ""
	FontSize = 14px
	Color = #dc3545
	FontWeight = bold
	Parent = LoginCard
END

// ===== EVENTS =====

// Page initialization
PROCEDURE InitializePage()
	gIsConnected = False
	gIDProfil = 0
	gUserEmail = ""
	gUserName = ""
	STC_INFO = "Veuillez vous connecter pour accéder au système"
END

// Login button click
PROCEDURE BTN_LOGIN_Click()
	STC_ERROR = ""
	STC_INFO = ""
	
	selectedProfile is int = COMBO_PROFILE
	email is string = EDT_EMAIL
	password is string = EDT_PASSWORD
	
	IF selectedProfile <= 0 THEN
		STC_ERROR = "Veuillez sélectionner un profil"
		RETURN
	END
	
	IF Length(email) = 0 THEN
		STC_ERROR = "Veuillez saisir votre adresse e-mail"
		RETURN
	END
	
	IF Length(password) = 0 THEN
		STC_ERROR = "Veuillez saisir votre mot de passe"
		RETURN
	END
	
	IF AuthenticateUser(selectedProfile, email, password) THEN
		STC_INFO = "Connexion réussie! Redirection en cours..."
		Multitask(1000)
		PageDisplay(page_main)
	ELSE
		loginAttempts = loginAttempts + 1
		STC_ERROR = "Identifiants incorrects (Tentative " + loginAttempts + "/" + maxLoginAttempts + ")"
		
		IF loginAttempts >= maxLoginAttempts THEN
			STC_ERROR = "Trop de tentatives de connexion. Accès bloqué."
			BTN_LOGIN..State = Grayed
		END
	END
END

// Quick access functions
PROCEDURE BTN_QUICK_PROFILE1_Click()
	COMBO_PROFILE = 1
	EDT_EMAIL = "<EMAIL>"
	EDT_PASSWORD = "admin123"
	BTN_LOGIN_Click()
END

PROCEDURE BTN_QUICK_PROFILE3_Click()
	COMBO_PROFILE = 2
	EDT_EMAIL = "<EMAIL>"
	EDT_PASSWORD = "user123"
	BTN_LOGIN_Click()
END

// Authentication function
FUNCTION AuthenticateUser(profileIndex is int, email is string, password is string) is boolean
	IF profileIndex = 1 AND email = "<EMAIL>" AND password = "admin123" THEN
		gIDProfil = 1
		gUserEmail = email
		gUserName = "Administrateur"
		gIsConnected = True
		RESULT True
	ELSE IF profileIndex = 2 AND email = "<EMAIL>" AND password = "user123" THEN
		gIDProfil = 3
		gUserEmail = email
		gUserName = "Utilisateur Standard"
		gIsConnected = True
		RESULT True
	ELSE IF email = "<EMAIL>" AND password = "test123" THEN
		gIDProfil = 1
		gUserEmail = email
		gUserName = "Bibouth Lep"
		gIsConnected = True
		RESULT True
	ELSE IF email = "<EMAIL>" AND password = "test123" THEN
		gIDProfil = 3
		gUserEmail = email
		gUserName = "Celine Bobo"
		gIsConnected = True
		RESULT True
	END
	
	RESULT False
END

// Page events
EVENT OnPageLoad = InitializePage
EVENT BTN_LOGIN.OnClick = BTN_LOGIN_Click
EVENT BTN_QUICK_PROFILE1.OnClick = BTN_QUICK_PROFILE1_Click
EVENT BTN_QUICK_PROFILE3.OnClick = BTN_QUICK_PROFILE3_Click
