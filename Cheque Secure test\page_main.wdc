[PAGE]
Name=page_main
Title=ChèqueSecure - Accueil
Description=Page d'accueil principale avec navigation vers tous les modules

[VARIABLES]
// Local variables for this page
userStats is Structure
    TotalChequiers is int
    ActiveChequiers is int
    TotalTransactions is int
    LastLoginDate is string
END

[CONTROLS]
// Main container with modern styling
MainContainer is Container {
    Width: 100%
    Height: 100vh
    BackgroundColor: #f8f9fa
    FontFamily: "Segoe UI, Tahoma, Geneva, Verdana, sans-serif"
}

// Header section
HeaderSection is Container {
    Width: 100%
    Height: 100px
    BackgroundColor: #007bff
    Color: white
    Display: flex
    AlignItems: center
    JustifyContent: center
    BoxShadow: 0 4px 8px rgba(0,0,0,0.1)
}

HeaderContent is Container {
    TextAlign: center
}

AppTitle is Static {
    Text: "ChèqueSecure"
    FontSize: 32px
    FontWeight: bold
    Color: white
    MarginBottom: 5px
}

AppSubtitle is Static {
    Text: "Système de Gestion Sécurisée des Chèques"
    FontSize: 16px
    Color: rgba(255,255,255,0.9)
}

// Content section
ContentSection is Container {
    Width: 100%
    Height: calc(100vh - 100px)
    Padding: 40px 20px
    OverflowY: auto
}

// Welcome section
WelcomeSection is Container {
    Width: 100%
    MaxWidth: 1200px
    Margin: 0 auto 40px auto
    BackgroundColor: white
    Border: 1px solid #dee2e6
    BorderRadius: 8px
    Padding: 30px
    BoxShadow: 0 2px 4px rgba(0,0,0,0.05)
}

WelcomeTitle is Static {
    Text: "Bienvenue dans ChèqueSecure"
    FontSize: 24px
    FontWeight: bold
    Color: #333
    MarginBottom: 15px
}

WelcomeText is Static {
    Text: "Gérez vos chéquiers et effectuez vos transactions en toute sécurité"
    FontSize: 16px
    Color: #6c757d
    MarginBottom: 20px
}

UserInfoText is Static {
    Text: "Utilisateur connecté - Profil ID: " + gIDProfil + " | Email: " + gUserEmail
    FontSize: 14px
    Color: #495057
    BackgroundColor: #f8f9fa
    Padding: 10px
    BorderRadius: 4px
}

// Main navigation section
NavigationSection is Container {
    Width: 100%
    MaxWidth: 1200px
    Margin: 0 auto
    Display: grid
    GridTemplateColumns: 1fr 1fr
    Gap: 30px
}

// Gestion card
GestionCard is Container {
    BackgroundColor: white
    Border: 2px solid #007bff
    BorderRadius: 12px
    Padding: 30px
    TextAlign: center
    BoxShadow: 0 4px 8px rgba(0,0,0,0.1)
    Cursor: pointer
    Transition: transform 0.2s ease
}

GestionIcon is Static {
    Text: "📋"
    FontSize: 48px
    MarginBottom: 20px
}

GestionTitle is Static {
    Text: "Gestion"
    FontSize: 20px
    FontWeight: bold
    Color: #007bff
    MarginBottom: 15px
}

GestionSubtitle is Static {
    Text: "Vos Chéquiers"
    FontSize: 16px
    Color: #6c757d
    MarginBottom: 20px
}

GestionDescription is Static {
    Text: "• Consulter l'historique de vos chéquiers\n• Mettre à jour la configuration\n• Associer de nouveaux profils\n• Suspendre des profils existants"
    FontSize: 14px
    Color: #495057
    TextAlign: left
    MarginBottom: 25px
    WhiteSpace: pre-line
}

BTN_GESTION is Button {
    Text: "Accéder à la Gestion"
    Width: 100%
    Height: 45px
    BackgroundColor: #007bff
    Color: white
    Border: none
    BorderRadius: 6px
    FontSize: 16px
    FontWeight: bold
    OnClick: BTN_GESTION_Click
    Cursor: pointer
}

// Emission card
EmissionCard is Container {
    BackgroundColor: white
    Border: 2px solid #28a745
    BorderRadius: 12px
    Padding: 30px
    TextAlign: center
    BoxShadow: 0 4px 8px rgba(0,0,0,0.1)
    Cursor: pointer
    Transition: transform 0.2s ease
}

EmissionIcon is Static {
    Text: "💳"
    FontSize: 48px
    MarginBottom: 20px
}

EmissionTitle is Static {
    Text: "Émission"
    FontSize: 20px
    FontWeight: bold
    Color: #28a745
    MarginBottom: 15px
}

EmissionSubtitle is Static {
    Text: "Chèques"
    FontSize: 16px
    Color: #6c757d
    MarginBottom: 20px
}

EmissionDescription is Static {
    Text: "• Retrait par chèque OTC\n• Paiement par chèque OTC\n• Émission de chèque classique\n• Émission de chèque prépayé"
    FontSize: 14px
    Color: #495057
    TextAlign: left
    MarginBottom: 25px
    WhiteSpace: pre-line
}

BTN_EMISSION is Button {
    Text: "Accéder à l'Émission"
    Width: 100%
    Height: 45px
    BackgroundColor: #28a745
    Color: white
    Border: none
    BorderRadius: 6px
    FontSize: 16px
    FontWeight: bold
    OnClick: BTN_EMISSION_Click
    Cursor: pointer
}

// Footer section
FooterSection is Container {
    Width: 100%
    MaxWidth: 1200px
    Margin: 40px auto 0 auto
    TextAlign: center
    Padding: 20px
    BorderTop: 1px solid #dee2e6
}

FooterText is Static {
    Text: "ChèqueSecure v1.0 - Système de Gestion Sécurisée des Chèques"
    FontSize: 12px
    Color: #6c757d
}

[FUNCTIONS]
// Page initialization
PROCEDURE InitializePage()
    // Check if user is connected
    IF NOT gIsConnected THEN
        Error("Veuillez vous connecter pour accéder à l'application")
        RETURN
    END
    
    // Update user info display
    UserInfoText = "Utilisateur connecté - Profil ID: " + gIDProfil + " | Email: " + gUserEmail
    
    // Load user statistics (demo data)
    LoadUserStatistics()
END

// Load user statistics
PROCEDURE LoadUserStatistics()
    userStats.TotalChequiers = 3
    userStats.ActiveChequiers = 2
    userStats.TotalTransactions = 15
    userStats.LastLoginDate = DateToString(DateSys(), "DD/MM/YYYY")
END

// Navigation functions
PROCEDURE BTN_GESTION_Click()
    PageDisplay("profilsOTC/page_chequiers")
END

PROCEDURE BTN_EMISSION_Click()
    PageDisplay("page_emission/page_chequeEmission")
END

[EVENTS]
// Page load event
OnPageLoad: InitializePage
