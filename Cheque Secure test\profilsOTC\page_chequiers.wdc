[PAGE]
Name=page_chequiers
Title=Vos Chéquiers
Description=Gestion des chéquiers de l'utilisateur connecté

[VARIABLES]
// Local variables for this page
lstChequiers is array of Chequier
lstProfils is array of Profil
selectedChequier is Chequier
selectedProfilIndex is int = -1
searchEmail is string
newProfil is Profil
bIsLoading is boolean = False

[CONTROLS]
// Main container with modern styling
MainContainer is Container {
    Width: 100%
    Height: 100vh
    BackgroundColor: #f8f9fa
    FontFamily: "Segoe UI, Tahoma, Geneva, Verdana, sans-serif"
}

// Header section with navigation
HeaderSection is Container {
    Width: 100%
    Height: 80px
    BackgroundColor: #007bff
    Color: white
    Display: flex
    AlignItems: center
    JustifyContent: space-between
    Padding: 0 20px
    BoxShadow: 0 2px 4px rgba(0,0,0,0.1)
}

HeaderTitle is Static {
    Text: "ChèqueSecure - Vos Chéquiers"
    FontSize: 24px
    FontWeight: bold
    Color: white
}

// Navigation menu
NavMenu is Container {
    Display: flex
    Gap: 20px
}

NavLinkGestion is Button {
    Text: "Gestion"
    BackgroundColor: transparent
    Color: white
    Border: 1px solid white
    Padding: 8px 16px
    BorderRadius: 4px
    OnClick: NavToGestion
}

NavLinkEmission is Button {
    Text: "Émission"
    BackgroundColor: transparent
    Color: white
    Border: 1px solid white
    Padding: 8px 16px
    BorderRadius: 4px
    OnClick: NavToEmission
}

// Content section with responsive layout
ContentSection is Container {
    Width: 100%
    Height: calc(100vh - 80px)
    Padding: 20px
    OverflowY: auto
}

// User info section
UserInfoSection is Container {
    Width: 100%
    BackgroundColor: white
    Border: 1px solid #dee2e6
    BorderRadius: 8px
    Padding: 15px
    MarginBottom: 20px
    BoxShadow: 0 2px 4px rgba(0,0,0,0.05)
}

UserInfoText is Static {
    Text: "Utilisateur connecté - Profil ID: " + gIDProfil
    FontSize: 14px
    Color: #6c757d
}

// Checkbooks list section
ChequiersSection is Container {
    Width: 100%
    BackgroundColor: white
    Border: 1px solid #dee2e6
    BorderRadius: 8px
    Padding: 20px
    MarginBottom: 20px
    BoxShadow: 0 2px 4px rgba(0,0,0,0.05)
}

SectionTitle is Static {
    Text: "Liste de vos chéquiers"
    FontSize: 18px
    FontWeight: bold
    MarginBottom: 15px
    BorderBottom: 2px solid #007bff
    PaddingBottom: 10px
    Color: #333
}

// Loading indicator
LoadingIndicator is Static {
    Text: "Chargement en cours..."
    FontSize: 14px
    Color: #6c757d
    TextAlign: center
    Visible: False
}

// Enhanced data table for checkbooks
TABLE_CHEQUIERS is Table {
    Width: 100%
    Height: 300px
    Columns: ["Numéro", "Type", "État", "Plafond", "Date Création"]
    DataSource: lstChequiers
    OnRowSelection: TABLE_CHEQUIERS_SelectRow
    BorderStyle: 1
    HeaderBackgroundColor: #f8f9fa
    AlternateRowColor: #f8f9fa
}

// Checkbook details section
ChequierDetailsSection is Container {
    Width: 100%
    BackgroundColor: white
    Border: 1px solid #dee2e6
    BorderRadius: 8px
    Padding: 20px
    MarginBottom: 20px
    BoxShadow: 0 2px 4px rgba(0,0,0,0.05)
    Visible: False
}

DetailsTitle is Static {
    Text: "Détails du chéquier sélectionné"
    FontSize: 16px
    FontWeight: bold
    MarginBottom: 15px
    Color: #333
}

// Checkbook configuration controls
ConfigContainer is Container {
    Display: flex
    FlexDirection: column
    Gap: 15px
}

PlafondLabel is Static {
    Text: "Plafond:"
    FontWeight: bold
}

EDT_PLAFOND is Edit {
    Placeholder: "Montant du plafond"
    Width: 200px
    Type: Currency
}

EtatLabel is Static {
    Text: "État:"
    FontWeight: bold
}

COMBO_ETAT is Combo {
    Width: 200px
    Content: ["Actif", "Inactif", "Suspendu"]
}

// Profile management section
ProfilsSection is Container {
    Width: 100%
    Height: 40%
    BackgroundColor: white
    Border: 1px solid #dee2e6
    BorderRadius: 8px
    Padding: 20px
}

ProfilsTitle is Static {
    Text: "Gestion des profils associés"
    FontSize: 18px
    FontWeight: bold
    MarginBottom: 15px
    BorderBottom: 2px solid #28a745
    PaddingBottom: 10px
}

// Search form
SearchForm is Container {
    Width: 100%
    Height: 60px
    MarginBottom: 15px
}

SearchLabel is Static {
    Text: "Rechercher par email:"
    Width: 150px
    VerticalAlign: middle
}

SearchInput is Edit {
    Width: 300px
    Placeholder: "Entrez l'email de l'utilisateur"
    DataSource: searchEmail
}

SearchButton is Button {
    Text: "Rechercher"
    Width: 100px
    BackgroundColor: #007bff
    Color: white
    OnClick: SearchUserByEmail
}

// Profile list
tblProfils is DataTable {
    Width: 100%
    Height: calc(100% - 120px)
    DataSource: lstProfils
    Columns: [
        {Name: "Email", Width: 250},
        {Name: "Nom", Width: 200},
        {Name: "État", Width: 100},
        {Name: "Actions", Width: 150}
    ]
}

// Action buttons
ActionButtons is Container {
    Width: 100%
    Height: 50px
    TextAlign: center
    MarginTop: 15px
}

AddProfilButton is Button {
    Text: "Ajouter Profil"
    Width: 120px
    BackgroundColor: #28a745
    Color: white
    OnClick: AddProfilToChequier
}

SuspendProfilButton is Button {
    Text: "Suspendre Profil"
    Width: 120px
    BackgroundColor: #dc3545
    Color: white
    OnClick: SuspendProfil
    MarginLeft: 10px
}

UpdateChequierButton is Button {
    Text: "Mettre à jour"
    Width: 120px
    BackgroundColor: #ffc107
    Color: black
    OnClick: UpdateChequierConfig
    MarginLeft: 10px
}

// Status and message controls
STC_INFO is Static {
    Text: ""
    Color: #28a745
    FontWeight: bold
    MarginTop: 10px
}

STC_ERROR is Static {
    Text: ""
    Color: #dc3545
    FontWeight: bold
    MarginTop: 10px
}

[FUNCTIONS]
// Page initialization
PROCEDURE InitializePage()
    // Check if user is connected with valid profile
    IF gIDProfil <> 1 AND gIDProfil <> 3 THEN
        STC_ERROR = "Accès non autorisé. Profil requis: 1 ou 3"
        RETURN
    END

    // Update user info display
    UserInfoText = "Utilisateur connecté - Profil ID: " + gIDProfil + " | Email: " + gUserEmail

    // Show loading indicator
    LoadingIndicator..Visible = True
    bIsLoading = True

    // Load user's checkbooks
    LoadUserChequiers()

    // Load associated profiles
    LoadAssociatedProfils()

    // Hide loading indicator
    LoadingIndicator..Visible = False
    bIsLoading = False

// Navigation functions
PROCEDURE NavToGestion()
    PageDisplay("profilsOTC/page_chequiers")
END

PROCEDURE NavToEmission()
    PageDisplay("page_emission/page_chequeEmission")
END

// Load user's checkbooks with enhanced data
PROCEDURE LoadUserChequiers()
    // Clear existing data
    lstChequiers = []

    // Sample checkbook data for testing
    chequier1 is Chequier
    chequier1.Numero = "0000159"
    chequier1.Type = "OTC"
    chequier1.Etat = "Actif"
    chequier1.Plafond = 50000
    chequier1.DateCreation = "01/01/2024"
    chequier1.IDProfil = gIDProfil
    chequier1.Activer = False
    chequier1.Signaler = False
    chequier1.Reserver = False
    lstChequiers.Add(chequier1)

    chequier2 is Chequier
    chequier2.Numero = "0000160"
    chequier2.Type = "Classique"
    chequier2.Etat = "Actif"
    chequier2.Plafond = 100000
    chequier2.DateCreation = "15/01/2024"
    chequier2.IDProfil = gIDProfil
    chequier2.Activer = False
    chequier2.Signaler = False
    chequier2.Reserver = False
    lstChequiers.Add(chequier2)

    chequier3 is Chequier
    chequier3.Numero = "0000161"
    chequier3.Type = "Prépayé"
    chequier3.Etat = "Actif"
    chequier3.Plafond = 25000
    chequier3.DateCreation = "20/01/2024"
    chequier3.IDProfil = gIDProfil
    chequier3.Activer = False
    chequier3.Signaler = False
    chequier3.Reserver = False
    lstChequiers.Add(chequier3)

    // Refresh the table display
    TABLE_CHEQUIERS..DataSource = lstChequiers
    STC_INFO = "Chéquiers chargés: " + lstChequiers..Count + " éléments"

// Load associated profiles with enhanced data
PROCEDURE LoadAssociatedProfils()
    // Clear existing data
    lstProfils = []

    // Sample profile data for testing
    profil1 is Profil
    profil1.IDProfil = 1
    profil1.Email = "<EMAIL>"
    profil1.Nom = "Lepine"
    profil1.Prenom = "Bibouth"
    profil1.Etat = "Actif"
    profil1.DateCreation = "01/01/2024"
    lstProfils.Add(profil1)

    profil2 is Profil
    profil2.IDProfil = 3
    profil2.Email = "<EMAIL>"
    profil2.Nom = "Bobo"
    profil2.Prenom = "Celine"
    profil2.Etat = "Actif"
    profil2.DateCreation = "05/01/2024"
    lstProfils.Add(profil2)

    // Refresh the table display
    TABLE_PROFILS..DataSource = lstProfils

// Event handlers for table selections
PROCEDURE TABLE_CHEQUIERS_SelectRow()
    selectedChequier = TABLE_CHEQUIERS..SelectedRow
    IF selectedChequier <> Null THEN
        // Show checkbook details
        ChequierDetailsSection..Visible = True
        EDT_PLAFOND = selectedChequier.Plafond
        COMBO_ETAT = selectedChequier.Etat
        BTN_UPDATE..State = Active
        STC_INFO = "Chéquier sélectionné: " + selectedChequier.Numero
    END
END

PROCEDURE TABLE_PROFILS_SelectRow()
    selectedProfilIndex = TABLE_PROFILS..SelectedRow
    IF selectedProfilIndex > 0 THEN
        BTN_SUSPEND..State = Active
        STC_INFO = "Profil sélectionné: " + lstProfils[selectedProfilIndex].Email
    END
END

// Search user by email
PROCEDURE SearchUserByEmail()
    IF searchEmail = "" THEN
        Error("Veuillez entrer une adresse email")
        RETURN
    END
    
    // In a real application, this would call an API
    // For demo purposes, we'll simulate the search
    IF searchEmail = "<EMAIL>" OR searchEmail = "<EMAIL>" THEN
        Info("Utilisateur trouvé: " + searchEmail)
        // Add to associated profiles list
        newProfil is Profil
        newProfil.Email = searchEmail
        newProfil.Nom = "Utilisateur trouvé"
        newProfil.Etat = "En attente"
        lstProfils.Add(newProfil)
    ELSE
        Error("Aucun utilisateur trouvé avec cet email")
    END

// Add profile to checkbook
PROCEDURE AddProfilToChequier()
    IF selectedChequier = NULL THEN
        Error("Veuillez sélectionner un chéquier")
        RETURN
    END
    
    IF searchEmail = "" THEN
        Error("Veuillez rechercher un utilisateur d'abord")
        RETURN
    END
    
    Info("Profil ajouté au chéquier: " + selectedChequier.Numero)

// Suspend profile
PROCEDURE SuspendProfil()
    selectedProfil is Profil = tblProfils.SelectedRow
    IF selectedProfil = NULL THEN
        Error("Veuillez sélectionner un profil")
        RETURN
    END
    
    selectedProfil.Etat = "Suspendu"
    Info("Profil suspendu: " + selectedProfil.Email)

// Update checkbook configuration
PROCEDURE UpdateChequierConfig()
    IF selectedChequier = NULL THEN
        Error("Veuillez sélectionner un chéquier")
        RETURN
    END
    
    Info("Configuration mise à jour pour le chéquier: " + selectedChequier.Numero)

PROCEDURE BTN_UPDATE_Click()
    IF selectedChequier = Null THEN
        STC_ERROR = "Veuillez sélectionner un chéquier"
        RETURN
    END

    // Update checkbook configuration
    selectedChequier.Plafond = EDT_PLAFOND
    selectedChequier.Etat = COMBO_ETAT
    selectedChequier.DateModification = DateSys()

    // Refresh table display
    TABLE_CHEQUIERS..DataSource = lstChequiers
    STC_INFO = "Configuration mise à jour pour le chéquier: " + selectedChequier.Numero
END

PROCEDURE BTN_SUSPEND_Click()
    IF selectedProfilIndex <= 0 THEN
        STC_ERROR = "Veuillez sélectionner un profil"
        RETURN
    END

    // Suspend the selected profile
    lstProfils[selectedProfilIndex].Etat = "Suspendu"
    lstProfils[selectedProfilIndex].DateModification = DateSys()

    // Refresh table display
    TABLE_PROFILS..DataSource = lstProfils
    STC_INFO = "Profil suspendu: " + lstProfils[selectedProfilIndex].Email

    // Reset selection
    selectedProfilIndex = -1
    BTN_SUSPEND..State = Grayed
END

[EVENTS]
// Page load event
OnPageLoad: InitializePage